[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poe]
include = "../../common/poe_shared_tasks.toml"

[tool.poe.tasks]
hooks = { "shell" = "../../.hooks/install.sh 'libs/data-sdk'" }
_test = "pytest -vvv --failed-first --random-order --color=yes"

[tool.poetry]
name = "data-sdk"
version = "0.0.0"
description = ""
authors = ["<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>"]
readme = "README.md"

[tool.ruff]
src = [".", "tests"]

[tool.ruff.lint]
select = [
    # Pyflakes
    "F",
    # Pycodestyle
    "E",
    "W",
    # isort
    "I",
]
ignore = ["E501", "E712"]


[tool.ruff.lint.isort]
known-first-party = ["data_sdk"]

[tool.poetry.dependencies]
python = "^3.11"
pandas = "^2.2.0"
pandera = { extras = ["mypy", "polars"], version = "^0.20.3" }
pyarrow = "^17.0.0"
pydantic-settings = "^2.1.0"
pydantic = "^2.6.1"
polars = "^0.20.10"
azure-storage-file-datalake = "^12.14.0"
azure-identity = "^1.15.0"
rich = "^13.7.1"
deltalake = "^0.16.4"
elastic-apm = "^6.22.3"

[tool.poetry.group.dev.dependencies]
ruff = "^0.4.5"
mypy = "^1.8.0"
pandas-stubs = "^2.1.4.231227"
poethepoet = "^0.26.1"
pre-commit = "^3.7.1"

[tool.poetry.group.test.dependencies]
pytest = "^8.2.2"
pytest-deadfixtures = "^2.2.1"
pytest-xdist = "^3.6.1"
pytest-random-order = "^1.1.1"

[[tool.poetry.source]]
name = "indiebi"
url = "https://gitlab.com/api/v4/projects/43340958/packages/pypi/simple"
priority = "explicit"
