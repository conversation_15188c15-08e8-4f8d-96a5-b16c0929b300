from __future__ import annotations

import abc
import os
from enum import Enum
from functools import singledispatchmethod
from io import Bytes<PERSON>
from pathlib import Path
from typing import Any

import polars as pl

from data_sdk.config import DLSConfig, LocalConfig
from data_sdk.connectors.azure_dls import get_filesystem_client


class PublicDataType(str, Enum):
    CURRENCY_EXCHANGE_RATES = "currency-exchange-rates"
    # STEAM_REVIEWS = "steam-reviews"


class CrawledPublicDataReader(abc.ABC):
    """
    Reader for accessing public data crawlers' output.

    Provides access to data stored in the `public-data-crawler` container/directory, organized by crawler types
    (like currency-exchange-rates, steam-reviews). Each crawler type can have multiple resources
    (directories) containing timestamped parquet snapshots.

    Reader is automatically selecting the most recent snapshop for specified resource.

    Example structure:
    ```
    public-data-crawlers
    ├── currency-exchange-rates
    │   └── result
    │       ├── 20241117T001027.parquet
    │       ├── 20241118T001028.parquet
    │       └── 20241119T081027.parquet
    ├── steam-reviews
    │   └── result
    │       ├── 20241117T003000.parquet
    │       └── 20241119T003024.parquet
    ...
    ```
    """

    @singledispatchmethod
    @staticmethod
    def get_reader(config: Any) -> "CrawledPublicDataReader":
        raise ValueError("Invalid source type")

    @get_reader.register
    @staticmethod
    def _(config: LocalConfig):
        return LocalCrawledPublicDataReader(config)

    @get_reader.register
    @staticmethod
    def _(config: DLSConfig):
        return DLSCrawledPublicDataReader(config)

    @abc.abstractmethod
    def read_resource(
        self,
        data_type: PublicDataType,
        resource_name: str = "result",
    ) -> pl.DataFrame:
        """Read newest parquet file from specified resource."""
        pass


class LocalCrawledPublicDataReader(CrawledPublicDataReader):
    def __init__(self, source_cfg: LocalConfig):
        self._local_path: Path = source_cfg.local_dir

    def read_resource(
        self,
        data_type: PublicDataType,
        resource_name: str = "result",
    ) -> pl.DataFrame:
        directory = self._local_path / data_type.value / resource_name
        newest_file = max(directory.glob("*.parquet"), key=os.path.getctime)
        return pl.read_parquet(newest_file)


class DLSCrawledPublicDataReader(CrawledPublicDataReader):
    def __init__(self, source_cfg: DLSConfig):
        self._client = get_filesystem_client(
            dls_account_name=source_cfg.account_name,
            file_system_name=source_cfg.container_name,
        )

    def read_resource(
        self,
        data_type: PublicDataType,
        resource_name: str = "result",
    ) -> pl.DataFrame:
        directory = f"{data_type.value}/{resource_name}/"
        all_files = list(self._client.get_paths(directory))
        if not all_files:
            raise FileNotFoundError(f"No files found in {directory}")

        newest_file = max(all_files, key=lambda x: x.last_modified)
        file_client = self._client.get_file_client(newest_file)
        file_content = file_client.download_file().readall()
        return pl.read_parquet(BytesIO(file_content))
