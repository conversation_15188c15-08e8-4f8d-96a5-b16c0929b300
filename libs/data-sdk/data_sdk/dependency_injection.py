from typing import Any

from data_sdk.custom_partition.reader import Custom<PERSON>art<PERSON><PERSON>eader
from data_sdk.domain import Portal
from data_sdk.domain.domain_types import ReportMetadataWithRawFile, StudioId
from data_sdk.domain.observations import ObservationType
from data_sdk.domain.tables import TableDefinition


def generate_init_kwargs(
    cls: Any,
    reader: CustomPartitionReader,
    studio_id: StudioId,
    portal: Portal | None = None,
    observation_type: ObservationType | None = None,
    dependencies: dict | None = None,
) -> dict[str, Any]:
    """
    Generates init function kwargs for a given class, injecting instances of:
    - TableDefinition instances for tables defined in the class

    Args:
        cls: The class for which to generate kwargs.
        reader: The CustomPartitionReader instance to read tables.
        studio_id: StudioId passed to the reader.
        portal: Portal passed to the reader.
        dependencies: A list of other dependencies to inject into the class.

    Returns:
        A dictionary of kwargs for the class's init function.
    """
    if dependencies is None:
        dependencies = {}

    init_kwargs: dict[str, Any] = {}
    for param_name, param_type in cls.__init__.__annotations__.items():
        if param_name == "return":
            continue  # Ignore return annotation

        if issubclass(param_type, TableDefinition):
            init_kwargs[param_name] = reader.read_table(
                table_cls=param_type,
                studio_id=studio_id,
                portal=portal,
                observation_type=observation_type,
            )
        elif param_type is StudioId:
            init_kwargs[param_name] = studio_id
        elif param_type is Portal:
            assert portal is not None
            init_kwargs[param_name] = portal
        elif param_type in dependencies:
            init_kwargs[param_name] = dependencies[param_type]
        else:
            if param_name == "raw_report" and param_type is ReportMetadataWithRawFile:
                continue  # Ignore raw_report parameter

            raise TypeError(
                f"Unsupported type for parameter {param_name}: {param_type}"
            )

    return init_kwargs
