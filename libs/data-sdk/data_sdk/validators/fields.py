from pandera import polars as pandera_polars

from data_sdk.validators.constants import StringLength


def BasicField(**kwargs) -> pandera_polars.Field:  # noqa: N802
    return pandera_polars.Field(coerce=True, **kwargs)


def NonNegativeNumberField(**kwargs) -> pandera_polars.Field:  # noqa: N802
    return pandera_polars.Field(
        ge=0,
        coerce=True,
        **kwargs,
    )


def TinyStringField(**kwargs) -> pandera_polars.Field:  # noqa: N802
    return pandera_polars.Field(
        str_length={"max_value": StringLength.TINY.value, "min_value": 1},
        coerce=True,
        **kwargs,
    )


def SmallStringField(**kwargs) -> pandera_polars.Field:  # noqa: N802
    return pandera_polars.Field(
        str_length={"max_value": StringLength.SMALL.value, "min_value": 1},
        coerce=True,
        **kwargs,
    )


def MediumStringField(**kwargs) -> pandera_polars.Field:  # noqa: N802
    return pandera_polars.Field(
        str_length={"max_value": StringLength.MEDIUM.value, "min_value": 1},
        coerce=True,
        **kwargs,
    )


def HugeStringField(**kwargs) -> pandera_polars.Field:  # noqa: N802
    return pandera_polars.Field(
        str_length={"max_value": StringLength.HUGE.value, "min_value": 1},
        coerce=True,
        **kwargs,
    )


def HashStringField(**kwargs) -> pandera_polars.Field:  # noqa: N802
    return pandera_polars.Field(
        str_length={
            "max_value": StringLength.TINY.value,
            "min_value": StringLength.TINY.value,
        },
        coerce=True,
        **kwargs,
    )
