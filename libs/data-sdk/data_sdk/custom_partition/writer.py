from __future__ import annotations

import abc
import logging
import os
from concurrent.futures import Thr<PERSON>PoolExecutor
from functools import singledispatchmethod
from io import Bytes<PERSON>
from pathlib import Path
from time import sleep
from typing import Any

import polars as pl
from azure.core.exceptions import ResourceModifiedError
from azure.storage.filedatalake import FileSystemClient

from data_sdk.config import DLSConfig, DummyConfig, Extension, LocalConfig
from data_sdk.connectors.azure_dls import get_filesystem_client, get_filesystem_token
from data_sdk.domain.tables import TableDefinition
from data_sdk.segmentator import SegmentDefinition

log = logging.getLogger(__name__)


class CustomPartitionsWriter(abc.ABC):
    @singledispatchmethod
    @staticmethod
    def get_writer(config: Any) -> CustomPartitionsWriter:
        raise ValueError("Invalid source type")

    @get_writer.register
    @staticmethod
    def _(config: DummyConfig):
        return DummyCustomPartitionsWriter()

    @get_writer.register
    @staticmethod
    def _(config: LocalConfig):
        return LocalCustomPartitionsWriter(config)

    @get_writer.register
    @staticmethod
    def _(config: DLSConfig):
        return DLSCustomPartitionWriter(config)

    @abc.abstractmethod
    def close(self): ...

    @abc.abstractmethod
    def save_table(
        self, table: TableDefinition, partition: str, segments: list[SegmentDefinition]
    ): ...


class DummyCustomPartitionsWriter(CustomPartitionsWriter):
    def close(self):
        log.info("Closing dummy writter")

    def save_table(self, table_to_save):
        log.info("Dummy save table %s", table_to_save)


class LocalCustomPartitionsWriter(CustomPartitionsWriter):
    def __init__(self, source_cfg: LocalConfig):
        self._local_dir: Path = source_cfg.local_dir
        self._extension = source_cfg.file_extension
        self._executor = ThreadPoolExecutor()

    def close(self):
        log.info("Closing Local writter")
        self._executor.shutdown(wait=True)

    def save_table(
        self, table: TableDefinition, partition: str, segments: list[SegmentDefinition]
    ):
        log.info("Local save table %s to dir %s", table.table_name, self._local_dir)
        results = self._executor.map(
            lambda segment: self._save_single_partition(
                table, partition=partition, segment=segment
            ),
            segments,
        )
        # Fetch all results to ensure all partitions are saved
        for r in results:
            pass

    def _write_file_with_directory_creation(self, data: pl.DataFrame, full_path: Path):
        full_path.parent.mkdir(parents=True, exist_ok=True)
        if self._extension == Extension.PARQUET:
            data.write_parquet(full_path)
        elif self._extension == Extension.CSV:
            data.write_csv(full_path)
        else:
            raise ValueError(f"Unsupported file extension {self._extension}")

    def _save_single_partition(
        self,
        table: TableDefinition,
        partition: str,
        segment: SegmentDefinition,
    ):
        full_path = (
            self._local_dir / partition / (segment.path + "." + self._extension.value)
        )
        exp = segment.mask_expression
        self._write_file_with_directory_creation(table.df.filter(exp), full_path)


class DLSCustomPartitionWriter(CustomPartitionsWriter):
    def __init__(self, source_cfg: DLSConfig):
        self._client: FileSystemClient = get_filesystem_client(
            dls_account_name=source_cfg.account_name,
            file_system_name=source_cfg.container_name,
        )
        self._base_dir = source_cfg.base_dir
        self._extension = source_cfg.file_extension
        self._executor = ThreadPoolExecutor()

    def close(self):
        self._executor.shutdown(wait=True)

    def save_table(
        self, table: TableDefinition, partition: str, segments: list[SegmentDefinition]
    ):
        results = self._executor.map(
            lambda segment: self._save_single_partition(
                table, partition=partition, segment=segment
            ),
            segments,
        )
        # Fetch all results to ensure all partitions are saved
        for r in results:
            pass

    def _write_file_to_bytes(
        self, table: TableDefinition, segment: SegmentDefinition
    ) -> BytesIO:
        bytes = BytesIO()
        exp = segment.mask_expression
        if self._extension == Extension.PARQUET:
            table.df.filter(exp).write_parquet(bytes)
            return bytes
        elif self._extension == Extension.CSV:
            table.df.filter(exp).write_csv(bytes)
            return bytes
        else:
            raise ValueError(f"Unsupported file extension {self._extension}")

    def _save_single_partition(
        self,
        table: TableDefinition,
        partition: str,
        segment: SegmentDefinition,
    ):
        bytes = self._write_file_to_bytes(table, segment)
        data_to_save = bytes.getvalue()
        full_path = (
            f"{self._base_dir}/{partition}/{segment.path}.{self._extension.value}"
        )
        file_client = self._client.get_file_client(full_path)
        try:
            file_client.upload_data(data_to_save, overwrite=True)
        except ResourceModifiedError as e:
            log.warning("Error while saving partition %s. Retrying in 10 seconds", e)
            sleep(10)
            file_client.upload_data(data_to_save, overwrite=True)


class DeltaWriter(abc.ABC):
    @singledispatchmethod
    @staticmethod
    def get_writer(config: Any, studio_id: int, base_path: str = ""):
        raise ValueError("Invalid source type")

    @get_writer.register
    @staticmethod
    def _(config: DummyConfig, studio_id: int, base_path: str = ""):
        return DummyDeltaWriter()

    @get_writer.register
    @staticmethod
    def _(config: LocalConfig, studio_id: int, base_path: str = ""):
        return LocalDeltaWriter(config, studio_id, base_path)

    @get_writer.register
    @staticmethod
    def _(config: DLSConfig, studio_id: int, base_path: str = ""):
        return DLSDeltaWriter(config, studio_id, base_path)


class DummyDeltaWriter(DeltaWriter):
    def close(self):
        log.info("Closing dummy  deltawritter")

    def save_table(self, table_to_save):
        log.info("Dummy Delta save table %s", table_to_save)


class LocalDeltaWriter(DeltaWriter):
    def __init__(self, source_cfg: LocalConfig, studio_id: int, base_path: str = ""):
        self._base_path = base_path + "/" if base_path else ""
        self._local_path = source_cfg.local_dir
        self._studio_id = studio_id

    def close(self):
        log.info("Closing Local Delta writter")

    def save_table(self, table_to_save):
        log.info("Local delta save table %s to dir %s", table_to_save, self._local_path)
        full_path = f"{self._local_path}/{self._base_path}{table_to_save.table_name}"
        directory = os.path.dirname(full_path)
        os.makedirs(directory, exist_ok=True)
        table_to_save.data.write_delta(
            full_path,
            mode="overwrite",
            delta_write_options={
                "partition_by": ["studio_id"],
                "predicate": f"studio_id = {self._studio_id}",
                "engine": "rust",
            },
        )


class DLSDeltaWriter(DeltaWriter):
    def __init__(self, source_cfg: DLSConfig, studio_id: int, base_path: str = ""):
        self._base_path = base_path + "/" if base_path else ""
        self._studio_id = studio_id
        self._token = get_filesystem_token()
        self._account_name = source_cfg.account_name
        self._container_name = source_cfg.container_name

    def close(self):
        log.info("Closing Local Delta writter")

    def save_table(self, table_to_save):
        full_path = f"abfss://{self._container_name}@{self._account_name}.dfs.core.windows.net/{self._base_path}{table_to_save.table_name}"
        table_to_save.data.write_delta(
            full_path,
            mode="overwrite",
            storage_options={
                "token": self._token,
            },
            delta_write_options={
                "partition_by": ["studio_id"],
                "predicate": f"studio_id = {self._studio_id}",
                "engine": "rust",
            },
        )
