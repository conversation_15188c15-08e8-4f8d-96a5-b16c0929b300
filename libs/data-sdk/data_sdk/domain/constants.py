import logging

log = logging.getLogger(__name__)

DEFAULT_SORTING_ORDER = 9
UNKNOWN = "Unknown"

# ids (values) are immutable, keys can be changed in case of naming issues
# or new ones can be added if there is new portal / platform / region
# we have Synapse check for that so if something will show up we will change these dicts accordingly
# final portal_platform_region_id looks like 171010

PORTAL_ID = {
    "Epic": 10,
    "GOG": 11,
    "Humble": 12,
    "Meta": 13,
    "Microsoft": 14,
    "Nintendo": 15,
    "PlayStation": 16,
    "Steam": 17,
    "Apple": 18,
    "Google": 19,
}
PLATFORM_ID = {
    "PC": 10,
    "Key": 11,
    "Quest": 12,
    "Rift": 13,
    "Microsoft": 14,
    "Switch": 15,
    "Wii U": 16,
    "Home": 17,
    "PS Vita": 18,
    "PS VR": 19,
    "PS2": 20,
    "PS3": 21,
    "PS4": 22,
    "PS5": 23,
    "PSP": 24,
    "PS VR2": 25,
    "iPhone": 26,
    "iPad": 27,
    "Desktop": 28,
    "iPod touch": 29,
    "Google": 30,
    "PSone": 31,
    "miniS": 32,
    "Switch 2": 33,
    "Unknown": 99,
}
REGION_ID = {
    "Global": 10,
    "Nintendo America": 11,
    "Nintendo Asia": 12,
    "Nintendo Europe": 13,
    "Nintendo Japan": 14,
    "PS America": 15,
    "PS Europe": 16,
    "PS Asia": 17,
    "PS Japan": 18,
    "PS Unknown": 19,
    "Nintendo China": 20,
}


def get_portal_platform_region_id(portal_platform_region: str) -> int:
    invalid_id = 0

    portal, platform, region = portal_platform_region.split(":")
    portal_id, platform_id, region_id = (
        PORTAL_ID.get(portal, invalid_id),
        PLATFORM_ID.get(platform, invalid_id),
        REGION_ID.get(region, invalid_id),
    )

    if not all([portal_id, platform_id, region_id]):
        log.error("Unmapped portal, platform or region %s", portal_platform_region)

    return portal_id * 10000 + platform_id * 100 + region_id
