from enum import Enum


class Store(str, Enum):
    EPIC = "Epic"
    GOG = "GOG"
    HUMBLE = "Humble"
    MICROSOFT = "Microsoft"
    NINTENDO_SWITCH_ASIA = "Nintendo Switch Asia"
    NINTENDO_SWITCH_AMERICA = "Nintendo Switch America"
    NINTENDO_SWITCH_EUROPE = "Nintendo Switch Europe"
    NINTENDO_SWITCH_JAPAN = "Nintendo Switch Japan"
    NINTENDO_SWITCH_CHINA = "Nintendo Switch China"
    NINTENDO_SWITCH_UNKNOWN = "Nintendo Switch Unknown"
    NINTENDO_SWITCH_2_ASIA = "Nintendo Switch 2 Asia"
    NINTENDO_SWITCH_2_AMERICA = "Nintendo Switch 2 America"
    NINTENDO_SWITCH_2_EUROPE = "Nintendo Switch 2 Europe"
    NINTENDO_SWITCH_2_JAPAN = "Nintendo Switch 2 Japan"
    NINTENDO_SWITCH_2_CHINA = "Nintendo Switch 2 China"
    NINTENDO_SWITCH_2_UNKNOWN = "Nintendo Switch 2 Unknown"
    NINTENDO_OTHER_ASIA = "Nintendo Other Asia"
    NINTENDO_OTHER_AMERICA = "Nintendo Other America"
    NINTENDO_OTHER_EUROPE = "Nintendo Other Europe"
    NINTENDO_OTHER_JAPAN = "Nintendo Other Japan"
    NINTENDO_OTHER_CHINA = "Nintendo Other China"
    NINTENDO_OTHER_UNKNOWN = "Nintendo Other Unknown"
    META_RIFT = "Meta Rift"
    META_QUEST = "Meta Quest"
    PLAYSTATION_AMERICA = "PlayStation America"
    PLAYSTATION_ASIA = "PlayStation Asia"
    PLAYSTATION_EUROPE = "PlayStation Europe"
    PLAYSTATION_JAPAN = "PlayStation Japan"
    PLAYSTATION_UNKNOWN = "PlayStation Unknown"
    STEAM = "Steam"
    APPLE = "Apple"
    GOOGLE = "Google"

    def abbreviate(self):
        abbreviations_map = {
            self.EPIC: self.value,
            self.GOG: self.value,
            self.MICROSOFT: self.value,
            self.STEAM: self.value,
            self.HUMBLE: self.value,
            self.NINTENDO_SWITCH_ASIA: "Switch AS",
            self.NINTENDO_SWITCH_AMERICA: "Switch US",
            self.NINTENDO_SWITCH_EUROPE: "Switch EU",
            self.NINTENDO_SWITCH_JAPAN: "Switch JP",
            self.NINTENDO_SWITCH_CHINA: "Switch CN",
            self.NINTENDO_SWITCH_UNKNOWN: "Switch Unknown",
            self.NINTENDO_SWITCH_2_ASIA: "Switch 2 AS",
            self.NINTENDO_SWITCH_2_AMERICA: "Switch 2 US",
            self.NINTENDO_SWITCH_2_EUROPE: "Switch 2 EU",
            self.NINTENDO_SWITCH_2_JAPAN: "Switch 2 JP",
            self.NINTENDO_SWITCH_2_CHINA: "Switch 2 CN",
            self.NINTENDO_SWITCH_2_UNKNOWN: "Switch 2 Unknown",
            self.NINTENDO_OTHER_ASIA: "Nintendo Other AS",
            self.NINTENDO_OTHER_AMERICA: "Nintendo Other US",
            self.NINTENDO_OTHER_EUROPE: "Nintendo Other EU",
            self.NINTENDO_OTHER_JAPAN: "Nintendo Other JP",
            self.NINTENDO_OTHER_CHINA: "Nintendo Other CN",
            self.NINTENDO_OTHER_UNKNOWN: "Nintendo Other Unknown",
            self.META_QUEST: "Quest",
            self.META_RIFT: "Rift",
            self.PLAYSTATION_AMERICA: "PS US",
            self.PLAYSTATION_ASIA: "PS AS",
            self.PLAYSTATION_EUROPE: "PS EU",
            self.PLAYSTATION_JAPAN: "PS JP",
            self.PLAYSTATION_UNKNOWN: "PS Unknown",
            self.APPLE: "Apple",
            self.GOOGLE: "Google",
        }
        return abbreviations_map[self]
