from enum import Enum

from data_sdk.domain.constants import DEFAULT_SORTING_ORDER


class Region(str, Enum):
    GLOBAL = "Global"
    NINTENDO_ASIA = "Nintendo Asia"
    NINTENDO_AMERICA = "Nintendo America"
    NINTENDO_EUROPE = "Nintendo Europe"
    NINTENDO_JAPAN = "Nintendo Japan"
    NINTENDO_CHINA = "Nintendo China"
    PLAYSTATION_AMERICA = "PS America"
    PLAYSTATION_ASIA = "PS Asia"
    PLAYSTATION_EUROPE = "PS Europe"
    PLAYSTATION_JAPAN = "PS Japan"
    PLAYSTATION_UNKNOWN = "PS Unknown"


_ps_region_dictionary = {
    "SIEA": Region.PLAYSTATION_AMERICA.value,
    "SIEE": Region.PLAYSTATION_EUROPE.value,
    "SIEAsia": Region.PLAYSTATION_ASIA.value,
    "SIEJ": Region.PLAYSTATION_JAPAN.value,
}


def get_ps_region_by_country_alpha_3(alpha_3):
    """
    Returns region if country id exists and throw an error otherwise
    >>> get_ps_region_by_country_alpha_3("SIEA")
    'PS America'
    >>> get_ps_region_by_country_alpha_3("ABC")
    'ABC'
    """
    return _ps_region_dictionary.get(alpha_3, Region.PLAYSTATION_UNKNOWN.value)


_nintendo_region_dictionary = {
    "AIA": Region.NINTENDO_AMERICA.value,  # Anguilla
    "AI": Region.NINTENDO_AMERICA.value,  # Anguilla
    "ATG": Region.NINTENDO_AMERICA.value,  # Antigua and Barbuda
    "AG": Region.NINTENDO_AMERICA.value,  # Antigua and Barbuda
    "ARG": Region.NINTENDO_AMERICA.value,  # Argentina
    "AR": Region.NINTENDO_AMERICA.value,  # Argentina
    "ABW": Region.NINTENDO_AMERICA.value,  # Aruba
    "AW": Region.NINTENDO_AMERICA.value,  # Aruba
    "AUS": Region.NINTENDO_EUROPE.value,  # Australia
    "AU": Region.NINTENDO_EUROPE.value,  # Australia
    "AUT": Region.NINTENDO_EUROPE.value,  # Austria
    "AT": Region.NINTENDO_EUROPE.value,  # Austria
    "BHS": Region.NINTENDO_AMERICA.value,  # Bahamas
    "BS": Region.NINTENDO_AMERICA.value,  # Bahamas
    "BRB": Region.NINTENDO_AMERICA.value,  # Barbados
    "BB": Region.NINTENDO_AMERICA.value,  # Barbados
    "BEL": Region.NINTENDO_EUROPE.value,  # Belgium
    "BE": Region.NINTENDO_EUROPE.value,  # Belgium
    "BLZ": Region.NINTENDO_AMERICA.value,  # Belize
    "BZ": Region.NINTENDO_AMERICA.value,  # Belize
    "BMU": Region.NINTENDO_AMERICA.value,  # Bermuda
    "BM": Region.NINTENDO_AMERICA.value,  # Bermuda
    "BOL": Region.NINTENDO_AMERICA.value,  # Bolivia
    "BO": Region.NINTENDO_AMERICA.value,  # Bolivia
    "BRA": Region.NINTENDO_AMERICA.value,  # Brazil
    "BR": Region.NINTENDO_AMERICA.value,  # Brazil
    "BGR": Region.NINTENDO_EUROPE.value,  # Bulgaria
    "BG": Region.NINTENDO_EUROPE.value,  # Bulgaria
    "CAN": Region.NINTENDO_AMERICA.value,  # Canada
    "CA": Region.NINTENDO_AMERICA.value,  # Canada
    "CYM": Region.NINTENDO_AMERICA.value,  # Cayman Islands
    "KY": Region.NINTENDO_AMERICA.value,  # Cayman Islands
    "CHL": Region.NINTENDO_AMERICA.value,  # Chile
    "CL": Region.NINTENDO_AMERICA.value,  # Chile
    "CHN": Region.NINTENDO_CHINA.value,  # China
    "CN": Region.NINTENDO_CHINA.value,  # China
    "COL": Region.NINTENDO_AMERICA.value,  # Colombia
    "CO": Region.NINTENDO_AMERICA.value,  # Colombia
    "CRI": Region.NINTENDO_AMERICA.value,  # Costa Rica
    "CR": Region.NINTENDO_AMERICA.value,  # Costa Rica
    "HRV": Region.NINTENDO_EUROPE.value,  # Croatia
    "HR": Region.NINTENDO_EUROPE.value,  # Croatia
    "CYP": Region.NINTENDO_EUROPE.value,  # Cyprus
    "CY": Region.NINTENDO_EUROPE.value,  # Cyprus
    "CZE": Region.NINTENDO_EUROPE.value,  # Czech Republic
    "CZ": Region.NINTENDO_EUROPE.value,  # Czech Republic
    "DNK": Region.NINTENDO_EUROPE.value,  # Denmark
    "DK": Region.NINTENDO_EUROPE.value,  # Denmark
    "DMA": Region.NINTENDO_AMERICA.value,  # Dominica
    "DM": Region.NINTENDO_AMERICA.value,  # Dominica
    "DOM": Region.NINTENDO_AMERICA.value,  # Dominican Republic
    "DO": Region.NINTENDO_AMERICA.value,  # Dominican Republic
    "ECU": Region.NINTENDO_AMERICA.value,  # Ecuador
    "EC": Region.NINTENDO_AMERICA.value,  # Ecuador
    "SLV": Region.NINTENDO_AMERICA.value,  # El Salvador
    "SV": Region.NINTENDO_AMERICA.value,  # El Salvador
    "EST": Region.NINTENDO_EUROPE.value,  # Estonia
    "EE": Region.NINTENDO_EUROPE.value,  # Estonia
    "FIN": Region.NINTENDO_EUROPE.value,  # Finland
    "FI": Region.NINTENDO_EUROPE.value,  # Finland
    "FRA": Region.NINTENDO_EUROPE.value,  # France
    "FR": Region.NINTENDO_EUROPE.value,  # France
    "GUF": Region.NINTENDO_AMERICA.value,  # French Guiana
    "GF": Region.NINTENDO_AMERICA.value,  # French Guiana
    "DEU": Region.NINTENDO_EUROPE.value,  # Germany
    "DE": Region.NINTENDO_EUROPE.value,  # Germany
    "GRC": Region.NINTENDO_EUROPE.value,  # Greece
    "GR": Region.NINTENDO_EUROPE.value,  # Greece
    "GRD": Region.NINTENDO_AMERICA.value,  # Grenada
    "GD": Region.NINTENDO_AMERICA.value,  # Grenada
    "GLP": Region.NINTENDO_AMERICA.value,  # Guadeloupe
    "GP": Region.NINTENDO_AMERICA.value,  # Guadeloupe
    "GTM": Region.NINTENDO_AMERICA.value,  # Guatemala
    "GT": Region.NINTENDO_AMERICA.value,  # Guatemala
    "GUY": Region.NINTENDO_AMERICA.value,  # Guyana
    "GY": Region.NINTENDO_AMERICA.value,  # Guyana
    "HTI": Region.NINTENDO_AMERICA.value,  # Haiti
    "HT": Region.NINTENDO_AMERICA.value,  # Haiti
    "HND": Region.NINTENDO_AMERICA.value,  # Honduras
    "HN": Region.NINTENDO_AMERICA.value,  # Honduras
    "HKG": Region.NINTENDO_ASIA.value,  # Hong Kong
    "HK": Region.NINTENDO_ASIA.value,  # Hong Kong
    "HUN": Region.NINTENDO_EUROPE.value,  # Hungary
    "HU": Region.NINTENDO_EUROPE.value,  # Hungary
    "IRL": Region.NINTENDO_EUROPE.value,  # Ireland
    "IE": Region.NINTENDO_EUROPE.value,  # Ireland
    "ISR": Region.NINTENDO_EUROPE.value,  # Israel
    "IL": Region.NINTENDO_EUROPE.value,  # Israel
    "ITA": Region.NINTENDO_EUROPE.value,  # Italy
    "IT": Region.NINTENDO_EUROPE.value,  # Italy
    "JAM": Region.NINTENDO_AMERICA.value,  # Jamaica
    "JM": Region.NINTENDO_AMERICA.value,  # Jamaica
    "JPN": Region.NINTENDO_JAPAN.value,  # Japan
    "JP": Region.NINTENDO_JAPAN.value,  # Japan
    "LVA": Region.NINTENDO_EUROPE.value,  # Latvia
    "LV": Region.NINTENDO_EUROPE.value,  # Latvia
    "LTU": Region.NINTENDO_EUROPE.value,  # Lithuania
    "LT": Region.NINTENDO_EUROPE.value,  # Lithuania
    "LUX": Region.NINTENDO_EUROPE.value,  # Luxembourg
    "LU": Region.NINTENDO_EUROPE.value,  # Luxembourg
    "MYS": Region.NINTENDO_AMERICA.value,  # Malaysia
    "MY": Region.NINTENDO_AMERICA.value,  # Malaysia
    "MLT": Region.NINTENDO_EUROPE.value,  # Malta
    "MT": Region.NINTENDO_EUROPE.value,  # Malta
    "MTQ": Region.NINTENDO_AMERICA.value,  # Martinique
    "MQ": Region.NINTENDO_AMERICA.value,  # Martinique
    "MEX": Region.NINTENDO_AMERICA.value,  # Mexico
    "MX": Region.NINTENDO_AMERICA.value,  # Mexico
    "MSR": Region.NINTENDO_AMERICA.value,  # Montserrat
    "MS": Region.NINTENDO_AMERICA.value,  # Montserrat
    "NLD": Region.NINTENDO_EUROPE.value,  # Netherlands
    "NL": Region.NINTENDO_EUROPE.value,  # Netherlands
    "ANT": Region.NINTENDO_AMERICA.value,  # Netherlands Antilles
    "AN": Region.NINTENDO_AMERICA.value,  # Netherlands Antilles
    "NZL": Region.NINTENDO_EUROPE.value,  # New Zealand
    "NZ": Region.NINTENDO_EUROPE.value,  # New Zealand
    "NIC": Region.NINTENDO_AMERICA.value,  # Nicaragua
    "NI": Region.NINTENDO_AMERICA.value,  # Nicaragua
    "NOR": Region.NINTENDO_EUROPE.value,  # Norway
    "NO": Region.NINTENDO_EUROPE.value,  # Norway
    "PAN": Region.NINTENDO_AMERICA.value,  # Panama
    "PA": Region.NINTENDO_AMERICA.value,  # Panama
    "PRY": Region.NINTENDO_AMERICA.value,  # Paraguay
    "PY": Region.NINTENDO_AMERICA.value,  # Paraguay
    "PER": Region.NINTENDO_AMERICA.value,  # Peru
    "PE": Region.NINTENDO_AMERICA.value,  # Peru
    "POL": Region.NINTENDO_EUROPE.value,  # Poland
    "PL": Region.NINTENDO_EUROPE.value,  # Poland
    "PRT": Region.NINTENDO_EUROPE.value,  # Portugal
    "PT": Region.NINTENDO_EUROPE.value,  # Portugal
    "ROU": Region.NINTENDO_EUROPE.value,  # Romania
    "RO": Region.NINTENDO_EUROPE.value,  # Romania
    "RUS": Region.NINTENDO_EUROPE.value,  # Russia
    "RU": Region.NINTENDO_EUROPE.value,  # Russia
    "KNA": Region.NINTENDO_AMERICA.value,  # Saint Kitts and Nevis
    "KN": Region.NINTENDO_AMERICA.value,  # Saint Kitts and Nevis
    "LCA": Region.NINTENDO_AMERICA.value,  # Saint Lucia
    "LC": Region.NINTENDO_AMERICA.value,  # Saint Lucia
    "VCT": Region.NINTENDO_AMERICA.value,  # Saint Vincent and the Grenadines
    "VC": Region.NINTENDO_AMERICA.value,  # Saint Vincent and the Grenadines
    "SAU": Region.NINTENDO_AMERICA.value,  # Saudi Arabia
    "SA": Region.NINTENDO_AMERICA.value,  # Saudi Arabia
    "SGP": Region.NINTENDO_AMERICA.value,  # Singapore
    "SG": Region.NINTENDO_AMERICA.value,  # Singapore
    "SVK": Region.NINTENDO_EUROPE.value,  # Slovakia
    "SK": Region.NINTENDO_EUROPE.value,  # Slovakia
    "SVN": Region.NINTENDO_EUROPE.value,  # Slovenia
    "SI": Region.NINTENDO_EUROPE.value,  # Slovenia
    "ZAF": Region.NINTENDO_EUROPE.value,  # South Africa
    "ZA": Region.NINTENDO_EUROPE.value,  # South Africa
    "ESP": Region.NINTENDO_EUROPE.value,  # Spain
    "ES": Region.NINTENDO_EUROPE.value,  # Spain
    "SUR": Region.NINTENDO_AMERICA.value,  # Suriname
    "SR": Region.NINTENDO_AMERICA.value,  # Suriname
    "SWE": Region.NINTENDO_EUROPE.value,  # Sweden
    "SE": Region.NINTENDO_EUROPE.value,  # Sweden
    "CHE": Region.NINTENDO_EUROPE.value,  # Switzerland
    "CH": Region.NINTENDO_EUROPE.value,  # Switzerland
    "TWN": Region.NINTENDO_ASIA.value,  # Taiwan
    "TW": Region.NINTENDO_ASIA.value,  # Taiwan
    "TTO": Region.NINTENDO_AMERICA.value,  # Trinidad and Tobago
    "TT": Region.NINTENDO_AMERICA.value,  # Trinidad and Tobago
    "TCA": Region.NINTENDO_AMERICA.value,  # Turks and Caicos Islands
    "TC": Region.NINTENDO_AMERICA.value,  # Turks and Caicos Islands
    "ARE": Region.NINTENDO_AMERICA.value,  # United Arab Emirates
    "AE": Region.NINTENDO_AMERICA.value,  # United Arab Emirates
    "GBR": Region.NINTENDO_EUROPE.value,  # United Kingdom
    "GB": Region.NINTENDO_EUROPE.value,  # United Kingdom
    "USA": Region.NINTENDO_AMERICA.value,  # United States
    "US": Region.NINTENDO_AMERICA.value,  # United States
    "URY": Region.NINTENDO_AMERICA.value,  # Uruguay
    "UY": Region.NINTENDO_AMERICA.value,  # Uruguay
    "VEN": Region.NINTENDO_AMERICA.value,  # Venezuela
    "VE": Region.NINTENDO_AMERICA.value,  # Venezuela
    "VGB": Region.NINTENDO_AMERICA.value,  # Virgin Islands, British
    "VG": Region.NINTENDO_AMERICA.value,  # Virgin Islands, British
    "VIR": Region.NINTENDO_AMERICA.value,  # Virgin Islands, U.S.
    "VI": Region.NINTENDO_AMERICA.value,  # Virgin Islands, U.S.
    "KOR": Region.NINTENDO_ASIA.value,  # South Korea
    "KR": Region.NINTENDO_ASIA.value,  # South Korea
}


def get_nintendo_region_by_country_code(alpha):
    """
    Returns region if country id exists and throw an error otherwise
    >>> get_nintendo_region_by_country_code("USA")
    'Nintendo America'
    >>> get_nintendo_region_by_country_code("ABC")
    Traceback (most recent call last):
    KeyError: 'ABC'
    """
    return _nintendo_region_dictionary[alpha]


_nintendo_wishlist_actions_region_dictionary = {
    "americas": Region.NINTENDO_AMERICA.value,
    "taiwan/hong kong": Region.NINTENDO_CHINA.value,
    "japan": Region.NINTENDO_JAPAN.value,
    "australia/europe": Region.NINTENDO_EUROPE.value,
    "korea": Region.NINTENDO_ASIA.value,
}


def get_nintendo_region_by_wishlist_actions_region(region):
    """
    Returns region if nintendo wishlist actions region exists and throw an error otherwise
    >>> get_nintendo_region_by_wishlist_actions_region("Americas")
    'Nintendo America'
    >>> get_nintendo_region_by_wishlist_actions_region("ABC")
    Traceback (most recent call last):
    KeyError: 'ABC'
    """
    try:
        return _nintendo_wishlist_actions_region_dictionary[region.lower()]
    except KeyError:
        return Region.GLOBAL.value


_region_sorting_order_map = {
    # global
    Region.GLOBAL.value: 0,
    # nintendo
    Region.NINTENDO_ASIA.value: 1,
    Region.NINTENDO_AMERICA.value: 2,
    Region.NINTENDO_EUROPE.value: 3,
    Region.NINTENDO_JAPAN.value: 4,
    Region.NINTENDO_CHINA.value: 5,
    # playstation
    Region.PLAYSTATION_AMERICA.value: 1,
    Region.PLAYSTATION_EUROPE.value: 2,
    Region.PLAYSTATION_JAPAN.value: 3,
    Region.PLAYSTATION_ASIA.value: 4,
    Region.PLAYSTATION_UNKNOWN.value: 9,
}


def get_sorting_order_for_region(region) -> int:
    """Return sorting priority for a given region (1-highest)

    Args:
        region: name of the region

    Returns:
        Number representing region sorting order. Defaults to 9 if region unrecognized.

    >>> get_sorting_order_for_region('Nintendo Japan')
    4
    >>> get_sorting_order_for_region('nonexistent')
    9
    """
    try:
        return _region_sorting_order_map[region]
    except KeyError:
        return DEFAULT_SORTING_ORDER
