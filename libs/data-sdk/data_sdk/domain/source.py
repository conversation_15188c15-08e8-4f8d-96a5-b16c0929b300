from enum import Enum

from data_sdk.domain import Portal
from data_sdk.domain.observations import ObservationType


class Source(str, Enum):
    APP_STORE_SALES = "app_store_sales"
    EPIC_SALES = "epic_sales"
    GOG_SALES = "gog_sales"
    GOOGLE_SALES = "google_sales"
    HUMBLE_SALES = "humble_sales"
    META_QUEST_SALES = "meta_quest_sales"
    META_RIFT_SALES = "meta_rift_sales"
    MICROSOFT_DAILY_ACTIVE_USERS = "microsoft_daily_active_users"
    MICROSOFT_SALES = "microsoft_sales"
    NINTENDO_CUMULATIVE_WISHLIST_SALES = "nintendo_cumulative_wishlist_sales"
    NINTENDO_DISCOUNTS = "nintendo_discounts"
    NINTENDO_SALES = "nintendo_sales"
    NINTENDO_WISHLIST_ACTIONS = "nintendo_wishlist_actions"
    PLAYSTATION_SALES = "playstation_sales"
    PLAYSTATION_WISHLIST_ACTIONS = "playstation_wishlist_actions"
    STEAM_DAILY_ACTIVE_USERS = "steam_daily_active_users"
    STEAM_DISCOUNTS = "steam_discounts"
    STEAM_IMPRESSIONS = "steam_impressions"
    STEAM_SALES = "steam_sales"
    STEAM_WISHLIST_ACTIONS = "steam_wishlist_actions"
    STEAM_WISHLIST_BALANCE = "steam_wishlist_balance"
    STEAM_WISHLIST_COHORTS = "steam_wishlist_cohorts"


# generate it from portal source map later on
source_to_portal: dict[Source, Portal] = {
    Source.APP_STORE_SALES: Portal.APPLE,
    Source.EPIC_SALES: Portal.EPIC,
    Source.GOG_SALES: Portal.GOG,
    Source.GOOGLE_SALES: Portal.GOOGLE,
    Source.HUMBLE_SALES: Portal.HUMBLE,
    Source.META_QUEST_SALES: Portal.META,
    Source.META_RIFT_SALES: Portal.META,
    Source.MICROSOFT_DAILY_ACTIVE_USERS: Portal.MICROSOFT,
    Source.MICROSOFT_SALES: Portal.MICROSOFT,
    Source.NINTENDO_CUMULATIVE_WISHLIST_SALES: Portal.NINTENDO,
    Source.NINTENDO_DISCOUNTS: Portal.NINTENDO,
    Source.NINTENDO_SALES: Portal.NINTENDO,
    Source.NINTENDO_WISHLIST_ACTIONS: Portal.NINTENDO,
    Source.PLAYSTATION_SALES: Portal.PLAYSTATION,
    Source.PLAYSTATION_WISHLIST_ACTIONS: Portal.PLAYSTATION,
    Source.STEAM_DAILY_ACTIVE_USERS: Portal.STEAM,
    Source.STEAM_DISCOUNTS: Portal.STEAM,
    Source.STEAM_IMPRESSIONS: Portal.STEAM,
    Source.STEAM_SALES: Portal.STEAM,
    Source.STEAM_WISHLIST_ACTIONS: Portal.STEAM,
    Source.STEAM_WISHLIST_BALANCE: Portal.STEAM,
    Source.STEAM_WISHLIST_COHORTS: Portal.STEAM,
}


def get_portal_by_source(source: Source) -> Portal:
    """
    Returns portal if sources exists and nothing otherwise
    >>> get_portal_by_source('steam_sales')
    'steam'
    >>> get_portal_by_source('imaginary_source') # doctest: +IGNORE_EXCEPTION_DETAIL
    Traceback (most recent call last):
        ...
    KeyError: 'DOES_NOT_EXIST'
    """
    return source_to_portal[source]


source_to_observation_type: dict[Source, ObservationType] = {
    Source.APP_STORE_SALES: ObservationType.SALES,
    Source.EPIC_SALES: ObservationType.SALES,
    Source.GOG_SALES: ObservationType.SALES,
    Source.GOOGLE_SALES: ObservationType.SALES,
    Source.HUMBLE_SALES: ObservationType.SALES,
    Source.META_QUEST_SALES: ObservationType.SALES,
    Source.META_RIFT_SALES: ObservationType.SALES,
    Source.MICROSOFT_DAILY_ACTIVE_USERS: ObservationType.DAILY_ACTIVE_USERS,
    Source.MICROSOFT_SALES: ObservationType.SALES,
    Source.NINTENDO_CUMULATIVE_WISHLIST_SALES: ObservationType.CUMULATIVE_WISHLIST_SALES,
    Source.NINTENDO_DISCOUNTS: ObservationType.DISCOUNTS,
    Source.NINTENDO_SALES: ObservationType.SALES,
    Source.NINTENDO_WISHLIST_ACTIONS: ObservationType.WISHLIST_ACTIONS,
    Source.PLAYSTATION_SALES: ObservationType.SALES,
    Source.PLAYSTATION_WISHLIST_ACTIONS: ObservationType.WISHLIST_ACTIONS,
    Source.STEAM_DAILY_ACTIVE_USERS: ObservationType.DAILY_ACTIVE_USERS,
    Source.STEAM_DISCOUNTS: ObservationType.DISCOUNTS,
    Source.STEAM_IMPRESSIONS: ObservationType.VISIBILITY,
    Source.STEAM_SALES: ObservationType.SALES,
    Source.STEAM_WISHLIST_ACTIONS: ObservationType.WISHLIST_ACTIONS,
    Source.STEAM_WISHLIST_BALANCE: ObservationType.WISHLIST_BALANCE,
    Source.STEAM_WISHLIST_COHORTS: ObservationType.WISHLIST_COHORTS,
}


def get_observation_type_by_source(source: Source) -> ObservationType:
    return source_to_observation_type[source]
