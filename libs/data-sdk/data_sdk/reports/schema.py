from __future__ import annotations

import abc
from typing import ClassVar, Type

import pandera as pa
import pandera.polars as pap
import polars as pl
from pydantic import BaseModel, ConfigDict, model_validator

from data_sdk.domain.observations import (
    CumulativeWishlistSalesModel,
    DailyActiveUsersModel,
    DiscountsModel,
    SalesModel,
    VisibilityModel,
    WishlistActionsModel,
    WishlistBalanceModel,
    WishlistCohortsModel,
)
from data_sdk.utils.pandera import empty_polars_df_from_model


class BaseConvertedReport(BaseModel, abc.ABC):
    model_config = ConfigDict(arbitrary_types_allowed=True)

    model: ClassVar[Type[pap.DataFrameModel]]
    df: pl.DataFrame

    @model_validator(mode="after")
    def validate_schema(self):
        if self.df.is_empty():
            self.df = empty_polars_df_from_model(self.model)
            return self
        self.df = self.model.validate(self.df)
        return self

    @classmethod
    def empty(cls) -> BaseConvertedReport:
        # schema will be set on validation
        return cls(df=pl.DataFrame())


class SalesConvertedReport(BaseConvertedReport):
    model: ClassVar[Type[pa.DataFrameModel]] = SalesModel


class VisibilityConvertedReport(BaseConvertedReport):
    model: ClassVar[Type[pa.DataFrameModel]] = VisibilityModel


class WishlistCohortsConvertedReport(BaseConvertedReport):
    model: ClassVar[Type[pa.DataFrameModel]] = WishlistCohortsModel


class WishlistActionsConvertedReport(BaseConvertedReport):
    model: ClassVar[Type[pa.DataFrameModel]] = WishlistActionsModel


class CumulativeWishlistSalesConvertedReport(BaseConvertedReport):
    model: ClassVar[Type[pa.DataFrameModel]] = CumulativeWishlistSalesModel


class WishlistBalanceConvertedReport(BaseConvertedReport):
    model: ClassVar[Type[pa.DataFrameModel]] = WishlistBalanceModel


class DailyActiveUsersConvertedReport(BaseConvertedReport):
    model: ClassVar[Type[pa.DataFrameModel]] = DailyActiveUsersModel


class DiscountsConvertedReport(BaseConvertedReport):
    model: ClassVar[Type[pa.DataFrameModel]] = DiscountsModel
