{"pipelines": [{"pipeline_name": "data_sharing", "pipeline_definition": {"INIT": {"success": ["update_shared"], "failure": []}, "update_shared": {"success": ["find_shards"], "failure": []}, "find_shards": {"success": ["direct_data_access_gold"], "failure": []}}, "triggered_by_events": ["DATA_SHARED", "DATA_UNSHARED"]}], "events": [{"name": "DATA_SHARED", "params_definition": ["studio_id"]}, {"name": "DATA_UNSHARED", "params_definition": ["studio_id"]}]}