from datetime import date, datetime
from pathlib import Path

import polars as pl

from core_silver.job import top_level_observation_converter
from data_sdk.domain import Portal
from data_sdk.domain.domain_types import StudioId
from data_sdk.domain.observations import ObservationType


def test_sales_conversion_missing_converted(
    external_steam_reports_factory,
    external_nintendo_reports_factory,
    nintendo_raw_sales_factory,
    tmp_path: Path,
    creation_datetime: datetime,
    get_tmp_path_files,
    mock_tables,
    mock_reports,
    local_config,
    external_currency_exchange_rates_table,
):
    mock_tables([external_currency_exchange_rates_table])

    mock_reports([
        {"external_reports_row": external_steam_reports_factory.build()},
        {
            "external_reports_row": external_nintendo_reports_factory.build(),
            "report": nintendo_raw_sales_factory(),
        },
    ])

    top_level_observation_converter(
        config=local_config,
        portal=Portal.NINTENDO,
        studio_id=StudioId(1),
        observation_type=ObservationType.SALES,
        creation_datetime=creation_datetime,
    )
    assert get_tmp_path_files() == [
        "converted/studio_id=1/1_2024-04-01_2024-04-01.parquet",
        "raw/NINTENDO-2024-04-01_2024-04-01.zip",
        "result/external_currency_exchange_rates/version=v1/20240401T120000Z.parquet",
        "result/external_reports/studio_id=1/version=v1/20240401T120000Z.parquet",
        "result/observation_sales/studio_id=1/portal=nintendo/version=v1/20240401T120000Z.parquet",
        "result/silver_reports/studio_id=1/portal=nintendo/observation_type=sales/version=v1/20240401T120000Z.parquet",
    ]

    converted_report_df = pl.read_parquet(
        tmp_path / "converted/studio_id=1/1_2024-04-01_2024-04-01.parquet",
        hive_partitioning=False,
    )

    assert converted_report_df.to_dicts() == [
        {
            "sku_id": "HACPAURNA",
            "portal": "Nintendo",
            "platform": "Switch",
            "region": "Nintendo Europe",
            "transaction_type": "Unknown",
            "payment_instrument": "Unknown",
            "tax_type": "Unknown",
            "sale_modificator": "NOT_APPLICABLE",
            "acquisition_platform": "Unknown",
            "acquisition_origin": "eShop",
            "iap_flag": "False",
            "date": date(2024, 4, 1),
            "retailer_tag": "NOT_APPLICABLE",
            "human_name": "SUPERHOT",
            "store_id": "SUPERHOT-Switch",
            "base_price_local": 39.99,
            "bundle_name": "NOT_APPLICABLE",
            "net_sales": 9.64,
            "gross_returned": 0.0,
            "gross_sales": 11.86,
            "units_returned": 0,
            "units_sold": 1,
            "free_units": 0,
            "price_local": 39.99,
            "price_usd": 11.86,
            "store": "Nintendo Switch Europe",
            "abbreviated_name": "Switch EU",
            "net_sales_approx": 6.75,
            "category": "Sale",
            "studio_id": 1,
            "report_id": 1,
            "unique_sku_id": "HACPAURNA-europe-nintendo:1",
            "country_code": "POL",
            "currency_code": "PLN",
            "hash_acquisition_properties": "928a40b30d3853b6153d65a453be371c",
            "portal_platform_region": "Nintendo:Switch:Nintendo Europe",
        }
    ]

    sales_schema_df = pl.read_parquet(
        tmp_path
        / "result/observation_sales/studio_id=1/portal=nintendo/version=v1/20240401T120000Z.parquet",
        hive_partitioning=False,
    ).sort(by="date", descending=True)

    assert sales_schema_df.to_dicts() == [
        {
            "report_id": 1,
            "portal": "Nintendo",
            "platform": "Switch",
            "date": date(2024, 4, 1),
            "sku_id": "HACPAURNA",
            "region": "Nintendo Europe",
            "transaction_type": "Unknown",
            "payment_instrument": "Unknown",
            "tax_type": "Unknown",
            "sale_modificator": "NOT_APPLICABLE",
            "acquisition_platform": "Unknown",
            "acquisition_origin": "eShop",
            "iap_flag": "False",
            "retailer_tag": "NOT_APPLICABLE",
            "human_name": "SUPERHOT",
            "store_id": "SUPERHOT-Switch",
            "base_price_local": 39.99,
            "bundle_name": "NOT_APPLICABLE",
            "net_sales": 9.64,
            "gross_returned": 0.0,
            "gross_sales": 11.86,
            "units_returned": 0,
            "units_sold": 1,
            "free_units": 0,
            "price_local": 39.99,
            "price_usd": 11.86,
            "store": "Nintendo Switch Europe",
            "abbreviated_name": "Switch EU",
            "net_sales_approx": 6.75,
            "category": "Sale",
            "studio_id": 1,
            "unique_sku_id": "HACPAURNA-europe-nintendo:1",
            "country_code": "POL",
            "currency_code": "PLN",
            "hash_acquisition_properties": "928a40b30d3853b6153d65a453be371c",
            "portal_platform_region": "Nintendo:Switch:Nintendo Europe",
        }
    ]


def test_sales_conversion_empty_report(
    external_steam_reports_factory,
    external_nintendo_reports_factory,
    nintendo_raw_sales_factory,
    tmp_path: Path,
    creation_datetime: datetime,
    get_tmp_path_files,
    mock_reports,
    local_config,
):
    mock_reports([
        {"external_reports_row": external_steam_reports_factory.build()},
        {
            "external_reports_row": external_nintendo_reports_factory.build(),
            "report": nintendo_raw_sales_factory(rows__units_sold=0),
        },
    ])

    top_level_observation_converter(
        config=local_config,
        portal=Portal.NINTENDO,
        studio_id=StudioId(1),
        observation_type=ObservationType.SALES,
        creation_datetime=creation_datetime,
    )
    assert get_tmp_path_files() == [
        "converted/studio_id=1/1_2024-04-01_2024-04-01.parquet",
        "raw/NINTENDO-2024-04-01_2024-04-01.zip",
        "result/external_reports/studio_id=1/version=v1/20240401T120000Z.parquet",
        "result/observation_sales/studio_id=1/portal=nintendo/version=v1/20240401T120000Z.parquet",
        "result/silver_reports/studio_id=1/portal=nintendo/observation_type=sales/version=v1/20240401T120000Z.parquet",
    ]

    converted_report_df = pl.read_parquet(
        tmp_path / "converted/studio_id=1/1_2024-04-01_2024-04-01.parquet",
        hive_partitioning=False,
    )

    assert converted_report_df.to_dicts() == []

    sales_schema_df = pl.read_parquet(
        tmp_path
        / "result/observation_sales/studio_id=1/portal=nintendo/version=v1/20240401T120000Z.parquet",
        hive_partitioning=False,
    ).sort(by="date", descending=True)

    assert sales_schema_df.to_dicts() == []
