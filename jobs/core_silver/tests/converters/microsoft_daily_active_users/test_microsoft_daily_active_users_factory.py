from datetime import date


def test_generate_empty_microsoft_daily_active_users(
    microsoft_raw_daily_active_users_factory,
):
    microsoft_raw_dau = microsoft_raw_daily_active_users_factory(
        start_date=date(2023, 10, 1), end_date=date(2023, 10, 1), rows=[]
    )
    assert microsoft_raw_dau.start_date == date(2023, 10, 1)
    assert microsoft_raw_dau.end_date == date(2023, 10, 1)
    assert microsoft_raw_dau.manifest_json == {
        "manifest.json": {
            "dateFrom": "2023-10-01",
            "dateTo": "2023-10-01",
            "fileMetaData": {
                "microsoft_daily_active_users-2023-10-01-2023-10-01.csv": {
                    "dateFrom": "2023-10-01",
                    "dateTo": "2023-10-01",
                    "observationType": "daily_active_users",
                    "rawData": True,
                },
            },
        },
    }
    assert microsoft_raw_dau.daily_active_users_csv == {
        "microsoft_daily_active_users-2023-10-01-2023-10-01.csv": "account,portal,date,product,product_id,count\n",
    }


def test_generate_one_day_of_microsoft_daily_active_users(
    microsoft_raw_daily_active_users_factory,
):
    result = microsoft_raw_daily_active_users_factory(
        start_date=date(2023, 10, 1),
        end_date=date(2023, 10, 1),
    )

    assert result.start_date == date(2023, 10, 1)
    assert result.end_date == date(2023, 10, 1)
    assert result.manifest_json == {
        "manifest.json": {
            "dateFrom": "2023-10-01",
            "dateTo": "2023-10-01",
            "fileMetaData": {
                "microsoft_daily_active_users-2023-10-01-2023-10-01.csv": {
                    "dateFrom": "2023-10-01",
                    "dateTo": "2023-10-01",
                    "observationType": "daily_active_users",
                    "rawData": True,
                },
            },
        },
    }
    assert result.daily_active_users_csv == {
        "microsoft_daily_active_users-2023-10-01-2023-10-01.csv": (
            "account,portal,date,product,product_id,count\n"
            "Test account,microsoft,2023-10-01,Test product,9NV17MJB26PG,1\n"
        ),
    }


def test_generate_two_days_of_microsoft_daily_active_users(
    microsoft_raw_daily_active_users_factory,
):
    result = microsoft_raw_daily_active_users_factory(
        start_date=date(2024, 4, 1),
        end_date=date(2024, 4, 2),
    )

    assert result.start_date == date(2024, 4, 1)
    assert result.end_date == date(2024, 4, 2)
    assert result.daily_active_users_csv == {
        "microsoft_daily_active_users-2024-04-01-2024-04-02.csv": (
            "account,portal,date,product,product_id,count\n"
            "Test account,microsoft,2024-04-01,Test product,9NV17MJB26PG,1\n"
            "Test account,microsoft,2024-04-02,Test product,9NV17MJB26PG,1\n"
        ),
    }


def test_generate_microsoft_daily_active_users_with_custom_data(
    microsoft_raw_daily_active_users_factory,
):
    result = microsoft_raw_daily_active_users_factory(
        start_date=date(2023, 10, 1),
        end_date=date(2023, 10, 1),
        rows__product="SUPERHOT WINDOWS 10",
        rows__product_id="9NV17MJB26PG",
        rows__count=14,
    )

    assert result.start_date == date(2023, 10, 1)
    assert result.end_date == date(2023, 10, 1)
    assert result.daily_active_users_csv == {
        "microsoft_daily_active_users-2023-10-01-2023-10-01.csv": (
            "account,portal,date,product,product_id,count\n"
            "Test account,microsoft,2023-10-01,SUPERHOT WINDOWS 10,9NV17MJB26PG,14\n"
        ),
    }


def test_generate_microsoft_daily_active_users_multiple_products(
    microsoft_raw_daily_active_users_factory,
):
    from tests.converters.microsoft_daily_active_users.conftest import (
        MicrosoftDailyActiveUsersRowFactory,
    )

    custom_rows = [
        MicrosoftDailyActiveUsersRowFactory(
            date=date(2023, 10, 1),
            product="SUPERHOT WINDOWS 10",
            product_id="9NV17MJB26PG",
            count=14,
        ),
        MicrosoftDailyActiveUsersRowFactory(
            date=date(2023, 10, 2),
            product="SUPERHOT WINDOWS 10",
            product_id="9NV17MJB26PG",
            count=8,
        ),
        MicrosoftDailyActiveUsersRowFactory(
            date=date(2023, 10, 1),
            product="SUPERHOT VR WINDOWS 10",
            product_id="9NBLGGH5FV99",
            count=5,
        ),
        MicrosoftDailyActiveUsersRowFactory(
            date=date(2023, 10, 2),
            product="SUPERHOT VR WINDOWS 10",
            product_id="9NBLGGH5FV99",
            count=3,
        ),
    ]

    result = microsoft_raw_daily_active_users_factory(
        start_date=date(2023, 10, 1),
        end_date=date(2023, 10, 2),
        rows=custom_rows,
    )

    assert result.start_date == date(2023, 10, 1)
    assert result.end_date == date(2023, 10, 2)
    assert result.daily_active_users_csv == {
        "microsoft_daily_active_users-2023-10-01-2023-10-02.csv": (
            "account,portal,date,product,product_id,count\n"
            "Test account,microsoft,2023-10-01,SUPERHOT WINDOWS 10,9NV17MJB26PG,14\n"
            "Test account,microsoft,2023-10-02,SUPERHOT WINDOWS 10,9NV17MJB26PG,8\n"
            "Test account,microsoft,2023-10-01,SUPERHOT VR WINDOWS 10,9NBLGGH5FV99,5\n"
            "Test account,microsoft,2023-10-02,SUPERHOT VR WINDOWS 10,9NBLGGH5FV99,3\n"
        ),
    }
