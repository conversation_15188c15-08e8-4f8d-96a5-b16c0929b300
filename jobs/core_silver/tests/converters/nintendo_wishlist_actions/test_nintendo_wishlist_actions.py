from datetime import date

from core_silver.observation_converter.converters.nintendo_wishlist_actions import (
    NintendoWishlistActionsConverter,
)


def test_convert_for_wishlist_actions_run(
    generate_raw_nintendo_wishlist_actions_report,
):
    raw_report = generate_raw_nintendo_wishlist_actions_report()
    converter = NintendoWishlistActionsConverter(raw_report)
    result = converter.convert()
    assert result


def test_convert_for_wishlist_actions_run_empty(
    generate_raw_nintendo_wishlist_actions_report,
):
    raw_report = generate_raw_nintendo_wishlist_actions_report(custom_rows_data=[])

    converter = NintendoWishlistActionsConverter(raw_report)
    result = converter.convert()
    assert result.df.is_empty()


def test_convert_with_one_line(
    generate_raw_nintendo_wishlist_actions_report,
):
    raw_report = generate_raw_nintendo_wishlist_actions_report()
    converter = NintendoWishlistActionsConverter(raw_report)
    result = converter.convert()
    assert len(result.df) == 1


def test_convert_with_three_lines(
    generate_raw_nintendo_wishlist_actions_report,
):
    raw_report = generate_raw_nintendo_wishlist_actions_report(rows=3)
    converter = NintendoWishlistActionsConverter(raw_report)
    result = converter.convert()
    assert len(result.df) == 3


def test_convert_for_three_days(
    nintendo_wishlist_actions_metadata_with_raw_file_factory,
):
    raw_report = nintendo_wishlist_actions_metadata_with_raw_file_factory(
        input_raw_file__end_date=date(2024, 4, 3)
    )
    converter = NintendoWishlistActionsConverter(raw_report)
    result = converter.convert()
    assert result.df["date"].to_list() == [
        date(2024, 4, 1),
        date(2024, 4, 2),
        date(2024, 4, 3),
    ]


def test_convert_for_three_days_and_two_rows(
    nintendo_wishlist_actions_metadata_with_raw_file_factory,
    nintendo_raw_wishlist_actions_factory,
):
    _expected_result = [
        {
            "platform": "Switch",
            "region": "Nintendo Europe",
            "portal": "Nintendo",
            "date": date(2024, 4, 1),
            "human_name": "SUPERHOT",
            "adds": 1,
            "deletes": 0,
            "purchases_and_activations": 0,
            "gifts": 0,
            "sku_id": "HACPAURNA",
            "store_id": "70010000020724",
            "store": "Nintendo Switch Europe",
            "abbreviated_name": "Switch EU",
            "report_id": 0,
            "studio_id": 1,
            "unique_sku_id": "HACPAURNA-europe-nintendo:1",
            "country_code": "ZZZ",
            "portal_platform_region": "Nintendo:Switch:Nintendo Europe",
        },
        {
            "platform": "Switch",
            "region": "Nintendo Europe",
            "portal": "Nintendo",
            "date": date(2024, 4, 1),
            "human_name": "SUPERHOT",
            "adds": 1,
            "deletes": 0,
            "purchases_and_activations": 0,
            "gifts": 0,
            "sku_id": "HACPATEST",
            "store_id": "70010000020724",
            "store": "Nintendo Switch Europe",
            "abbreviated_name": "Switch EU",
            "report_id": 0,
            "studio_id": 1,
            "unique_sku_id": "HACPATEST-europe-nintendo:1",
            "country_code": "ZZZ",
            "portal_platform_region": "Nintendo:Switch:Nintendo Europe",
        },
        {
            "platform": "Switch",
            "region": "Nintendo Europe",
            "portal": "Nintendo",
            "date": date(2024, 4, 2),
            "human_name": "SUPERHOT",
            "adds": 1,
            "deletes": 0,
            "purchases_and_activations": 0,
            "gifts": 0,
            "sku_id": "HACPAURNA",
            "store_id": "70010000020724",
            "store": "Nintendo Switch Europe",
            "abbreviated_name": "Switch EU",
            "report_id": 0,
            "studio_id": 1,
            "unique_sku_id": "HACPAURNA-europe-nintendo:1",
            "country_code": "ZZZ",
            "portal_platform_region": "Nintendo:Switch:Nintendo Europe",
        },
        {
            "platform": "Switch",
            "region": "Nintendo Europe",
            "portal": "Nintendo",
            "date": date(2024, 4, 2),
            "human_name": "SUPERHOT",
            "adds": 1,
            "deletes": 0,
            "purchases_and_activations": 0,
            "gifts": 0,
            "sku_id": "HACPATEST",
            "store_id": "70010000020724",
            "store": "Nintendo Switch Europe",
            "abbreviated_name": "Switch EU",
            "report_id": 0,
            "studio_id": 1,
            "unique_sku_id": "HACPATEST-europe-nintendo:1",
            "country_code": "ZZZ",
            "portal_platform_region": "Nintendo:Switch:Nintendo Europe",
        },
    ]

    _end_date = date(2024, 4, 2)
    raw_report = nintendo_wishlist_actions_metadata_with_raw_file_factory(
        input_raw_file=nintendo_raw_wishlist_actions_factory(end_date=_end_date)
        + nintendo_raw_wishlist_actions_factory(
            end_date=_end_date, rows__code="HACPATEST"
        )
    )
    converter = NintendoWishlistActionsConverter(raw_report)
    result = converter.convert()
    assert result.df.to_dicts() == _expected_result


def test_convert_with_one_sku_with_different_regions(
    generate_raw_nintendo_wishlist_actions_report,
):
    raw_report = generate_raw_nintendo_wishlist_actions_report(
        custom_rows_data=[
            {"nsuid": "70000000000001", "region": "Americas"},
            {"nsuid": "70000000000002", "region": "Unexpected region"},
        ]
    )
    converter = NintendoWishlistActionsConverter(raw_report)
    result = converter.convert()
    assert len(result.df) == 2
    assert result.df["unique_sku_id"].to_list() == [
        "HACPAURNA-america-nintendo:1",
        "HACPAURNA-global-nintendo:1",
    ]


def test_convert_with_two_skus_with_different_regions(
    generate_raw_nintendo_wishlist_actions_report,
):
    raw_report = generate_raw_nintendo_wishlist_actions_report(
        custom_rows_data=[
            {"nsuid": "70000000000001", "code": "HACPAURNA", "region": "Americas"},
            {
                "nsuid": "70000000000002",
                "code": "HACPATEST",
                "region": "Unexpected region",
            },
        ]
    )
    converter = NintendoWishlistActionsConverter(raw_report)
    result = converter.convert()
    assert len(result.df) == 2
    assert result.df["unique_sku_id"].to_list() == [
        "HACPAURNA-america-nintendo:1",
        "HACPATEST-global-nintendo:1",
    ]


def test_convert_with_only_zero_additions(
    generate_raw_nintendo_wishlist_actions_report,
):
    raw_report = generate_raw_nintendo_wishlist_actions_report(
        custom_rows_data=[{"wishlist_add": 0}]
    )
    converter = NintendoWishlistActionsConverter(raw_report)

    result = converter.convert()
    assert result.df.to_dicts() == []


def test_convert_nintendo_switch_2_wishlists(
    generate_raw_nintendo_wishlist_actions_report,
):
    raw_report = generate_raw_nintendo_wishlist_actions_report(
        custom_rows_data=[
            {
                "platform": "Switch 2",
                "region": "Taiwan/Hong Kong",
            }
        ]
    )
    converter = NintendoWishlistActionsConverter(raw_report)
    result = converter.convert()
    assert len(result.df) == 1
    assert result.df["platform"].to_list() == ["Switch 2"]
