from datetime import date, datetime
from pathlib import Path
from typing import Any, TypedDict

import polars as pl
import pytest

from core_silver.job import top_level_observation_converter
from data_sdk.custom_partition.partitioner import get_table_partition
from data_sdk.custom_partition.writer import CustomPartitionsWriter
from data_sdk.domain import Portal
from data_sdk.domain.domain_types import StudioId
from data_sdk.domain.observations import ObservationType
from data_sdk.domain.tables import TableDefinition
from data_sdk.segmentator import BaseSegmentator, SingleSegmentator
from data_sdk.utils.date_utils import datetime_to_string


@pytest.fixture
def mock_tables(local_config, creation_datetime):
    writer = CustomPartitionsWriter.get_writer(local_config.output_cfg)
    segmentator: BaseSegmentator = SingleSegmentator()  # TODO: this should be dynamic

    def _mock_tables(tables: list[TableDefinition]):
        for table in tables:
            partition = get_table_partition(table, studio_id=1)
            segments = segmentator.create_segments(table, creation_datetime)
            writer.save_table(table, partition, segments)

    return _mock_tables


class MockedReport(TypedDict):
    external_reports_row: dict[str, Any]
    report: dict[str, Any] | None
    converted_df: pl.DataFrame | None


@pytest.fixture
def mock_reports(external_reports_path: Path, tmp_path: Path):
    def _mock_reports(mocked_reports: list[MockedReport]):
        external_report_rows = []

        for mocked_report in mocked_reports:
            row = mocked_report["external_reports_row"]
            external_report_rows.append(row)

            report = mocked_report.get("report")
            if report is not None:
                blob_name = row["blob_name"]
                zip_path: Path = tmp_path / "raw" / blob_name
                zip_path.parent.mkdir(exist_ok=True)
                zip_path.write_bytes(report.raw_zip_file)  # type: ignore[]

            converted_df = mocked_report.get("converted_df")
            if converted_df is not None:
                path = tmp_path / "converted" / f"studio_id={row['studio_id']}"
                path.mkdir(parents=True, exist_ok=True)
                filename = generate_expected_filename(row)
                converted_df.write_parquet(path / filename)

        reports_df = pl.DataFrame(data=external_report_rows)
        reports_df.write_parquet(external_reports_path)

    return _mock_reports


def generate_expected_filename(row) -> str:
    return f"{row['report_id']}_{row['date_from']}_{row['date_to']}.parquet"


@pytest.fixture
def external_reports_path(tmp_path, creation_datetime) -> Path:
    result = (
        tmp_path
        / "result/external_reports/studio_id=1/version=v1/"
        / f"{datetime_to_string(creation_datetime)}.parquet"
    )
    result.parent.mkdir(parents=True, exist_ok=True)
    return result


@pytest.fixture
def creation_datetime() -> datetime:
    return datetime(year=2024, month=4, day=1, hour=12, minute=0)


def test_nintendo_cumulative_wishlist_sales_conversion_two_consecutive_reports(
    external_nintendo_cumulative_wishlist_sales_reports_factory,
    nintendo_raw_wishlist_actions_factory,
    tmp_path: Path,
    mock_tables,
    mock_reports,
    local_config,
    external_currency_exchange_rates_table,
):
    mock_tables([external_currency_exchange_rates_table])

    report_11 = nintendo_raw_wishlist_actions_factory(
        start_date=date(2024, 3, 1),
        end_date=date(2024, 3, 3),
    )
    report_12 = nintendo_raw_wishlist_actions_factory(
        start_date=date(2024, 3, 2),
        end_date=date(2024, 3, 4),
    )
    mock_reports([
        {
            "external_reports_row": external_nintendo_cumulative_wishlist_sales_reports_factory.build(
                report_id=11,
                date_from=date(2024, 3, 1),
                date_to=date(2024, 3, 3),
            ),
            "report": report_11,
        },
        {
            "external_reports_row": external_nintendo_cumulative_wishlist_sales_reports_factory.build(
                report_id=12,
                date_from=date(2024, 3, 2),
                date_to=date(2024, 3, 4),
            ),
            "report": report_12,
        },
    ])

    top_level_observation_converter(
        config=local_config,
        portal=Portal.NINTENDO,
        studio_id=StudioId(1),
        observation_type=ObservationType.CUMULATIVE_WISHLIST_SALES,
        creation_datetime=datetime(2024, 4, 4, 12, 0, 0),
    )

    cumulative_wishlist_sales_schema_df = pl.read_parquet(
        tmp_path
        / "result/observation_cumulative_wishlist_sales/studio_id=1/portal=nintendo/version=v1/20240404T120000Z.parquet",
        hive_partitioning=False,
    ).sort(by="date", descending=True)

    assert cumulative_wishlist_sales_schema_df.shape == (2, 16)

    assert cumulative_wishlist_sales_schema_df.to_dicts() == [
        {
            "platform": "Switch",
            "region": "Nintendo Europe",
            "portal": "Nintendo",
            "date": date(2024, 3, 4),
            "cumulative_wishlist_sales": 300,
            "source_based_conversion_rate": "0.01%",
            "source_based_total_downloads": 3000000,
            "human_name": "SUPERHOT",
            "sku_id": "HACPAURNA",
            "store_id": "70010000020724",
            "store": "Nintendo Switch Europe",
            "abbreviated_name": "Switch EU",
            "report_id": 12,
            "studio_id": 1,
            "unique_sku_id": "HACPAURNA-europe-nintendo:1",
            "portal_platform_region": "Nintendo:Switch:Nintendo Europe",
        },
        {
            "platform": "Switch",
            "region": "Nintendo Europe",
            "portal": "Nintendo",
            "date": date(2024, 3, 3),
            "cumulative_wishlist_sales": 300,
            "source_based_conversion_rate": "0.01%",
            "source_based_total_downloads": 3000000,
            "human_name": "SUPERHOT",
            "sku_id": "HACPAURNA",
            "store_id": "70010000020724",
            "store": "Nintendo Switch Europe",
            "abbreviated_name": "Switch EU",
            "report_id": 11,
            "studio_id": 1,
            "unique_sku_id": "HACPAURNA-europe-nintendo:1",
            "portal_platform_region": "Nintendo:Switch:Nintendo Europe",
        },
    ]
