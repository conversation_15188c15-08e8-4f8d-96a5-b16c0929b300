import contextlib
import logging
import shutil
from enum import Enum
from pathlib import Path

import typer
from azure.identity import DefaultAzureCredential
from azure.keyvault.secrets import SecretClient
from pipeline_sdk.monitoring.logs import configure_logger

from core_silver.config import Config
from core_silver.external_sources.connectors.api import APIConfig
from core_silver.job import JobInputParameters, run
from data_sdk.config import DLSConfig, DummyConfig, LocalConfig
from data_sdk.domain import Portal
from data_sdk.domain.domain_types import StudioId
from data_sdk.domain.observations import ObservationType
from data_sdk.logging import force_human_readable_handler_for_tty

configure_logger(
    custom_loggers_config={
        "core_silver": {"level": "INFO"},
        "data_sdk": {"level": "INFO"},
    }
)

force_human_readable_handler_for_tty()

log = logging.getLogger(__name__)


class InputEnv(str, Enum):
    DEV = "dev"
    PROD = "prod"
    LOCAL = "local"


class OutputEnv(str, Enum):
    DEV = "dev"
    LOCAL = "local"
    DRY = "dry"


class RunType(str, Enum):
    DELTA = "delta"
    CUSTOM_PARTITIONS = "cp"
    BOTH = "both"


def main(
    input_env: InputEnv = InputEnv.LOCAL,
    output_env: OutputEnv = OutputEnv.LOCAL,
    studio_id: int = 1,
    observation_type: ObservationType = ObservationType.SALES,
    portal: Portal = Portal.STEAM,
    _run_type: RunType = RunType.BOTH,
    local_output_dir: str = None,
    clean: bool = False,  # noqa: FBT001, FBT002
):
    config = Config()
    if input_env == InputEnv.DEV:
        vault_client = SecretClient(
            vault_url="https://kv-single-click-dev-b0.vault.azure.net",
            credential=DefaultAzureCredential(),
        )

        config.input_cfg = DLSConfig(
            account_name="strawstoragedevbr",
            container_name="raw-reports",
        )
        config.crawled_public_data_cfg = DLSConfig(
            account_name="dlsaggregateddevs7",
            container_name="public-data-crawlers",
        )
        config.report_service = APIConfig(
            url="https://report-service.indiebi.dev",
            api_key=vault_client.get_secret("report-service-api-key").value,
        )
        config.user_service = APIConfig(
            url="https://user-service-v2.indiebi.dev",
            api_key=vault_client.get_secret("user-service-v2-api-key").value,
        )

    elif input_env == InputEnv.PROD:
        config.input_cfg = DLSConfig(
            account_name="strawstorageprodb9",
            container_name="raw-reports",
        )
    if output_env == OutputEnv.LOCAL and local_output_dir is not None:
        config.converted_reports_cfg = LocalConfig(local_dir=Path(local_output_dir))
        config.output_cfg = LocalConfig(local_dir=Path(local_output_dir))

    if output_env == OutputEnv.LOCAL and clean:
        with contextlib.suppress(FileNotFoundError):
            log.info(
                f"Cleaning previous results output directories {str(config.converted_reports_cfg.local_dir)}"
            )
            shutil.rmtree(config.converted_reports_cfg.local_dir)
        with contextlib.suppress(FileNotFoundError):
            log.info(
                f"Cleaning previous results output directories {str(config.output_cfg.local_dir)}"
            )
            shutil.rmtree(config.output_cfg.local_dir)

    if output_env == OutputEnv.DRY:
        config.output_cfg = DummyConfig()

    elif output_env == OutputEnv.DEV:
        config.converted_reports_cfg = DLSConfig(
            account_name="dlsaggregateddevs7",
            container_name="core-silver",
            base_dir="converted",
        )
        config.output_cfg = DLSConfig(
            account_name="dlsaggregateddevs7",
            container_name="core-silver",
            base_dir="result",
        )

    run(
        params=JobInputParameters(
            studio_id=StudioId(studio_id),
            observation_type=observation_type,
            portal=portal,
        ),
        config=config,
    )


if __name__ == "__main__":
    typer.run(main)
