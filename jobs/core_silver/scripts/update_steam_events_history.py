import fire
import pandas as pd

DATE_FORMAT = "%Y-%-m-%-d"

LINK_TO_STEAM_EVENTS = "https://docs.google.com/spreadsheets/d/e/2PACX-1vSknpYoQ5QLuFtCCAuau-zjhVoiwSWkCZ962L-lNqFUtBrKsnea7Xt7XrUOEBPUKGIrpIHWzBMb5E-U/pub?gid=0&single=true&output=csv"
CSV_PATH = "core_silver/external_sources/static/steam_events_history.csv"

expected_steam_schema = {
    "start_year": int,
    "start_date": str,
    "end_date": str,
    "major": int,
    "name": str,
    "start_day": int,
    "start_month": int,
    "end_day": int,
    "end_month": int,
    "end_year": int,
}


class DataFrameRowsMismatch(Exception):
    def __init__(self, message: str | None = None):
        self.message = message

    def __repr__(self) -> str:
        s = self.__class__.__name__
        return s if not self.message else f'{s}: "{self.message}"'


class SourceFileSchemaError(Exception):
    pass


def _validate_steam_event_data(df: pd.DataFrame, schema: dict) -> bool:
    for column, data_type in schema.items():
        if not all(isinstance(x, data_type) for x in df[column]):
            print(f"Invalid data type in column '{column}'")
            return False
    return True


def _compare_files(steam_events_history: pd.DataFrame) -> None:
    current_file = pd.read_csv(CSV_PATH)

    actual_rows = len(current_file)
    expected_rows = len(steam_events_history)

    if actual_rows != expected_rows:
        raise DataFrameRowsMismatch(
            message=f"Number of rows don't match: {actual_rows} != {expected_rows}"
        )

    diff = current_file.compare(steam_events_history)

    if len(diff) != 0:
        raise DataFrameRowsMismatch(
            message=f"DataFrames are different, diff:\n{diff.to_string(max_rows=20)}"
        )


def run(*, check: bool = False):
    steam_events_history = pd.read_csv(filepath_or_buffer=LINK_TO_STEAM_EVENTS).assign(
        start_date=lambda df: pd.to_datetime(df["start_date"]).dt.strftime(DATE_FORMAT),
        end_date=lambda df: pd.to_datetime(df["end_date"]).dt.strftime(DATE_FORMAT),
    )

    is_data_valid = _validate_steam_event_data(
        steam_events_history, expected_steam_schema
    )
    if not is_data_valid:
        raise SourceFileSchemaError

    if check:
        _compare_files(steam_events_history)
    else:
        steam_events_history["sort_by"] = pd.to_datetime(
            steam_events_history["start_date"], dayfirst=True
        )
        steam_events_history = steam_events_history.sort_values(
            "sort_by", ascending=False
        )
        steam_events_history = steam_events_history.drop("sort_by", axis=1)
        steam_events_history.to_csv(CSV_PATH, index=False)


if __name__ == "__main__":
    fire.Fire(run)
