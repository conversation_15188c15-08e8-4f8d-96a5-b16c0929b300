from enum import Enum


class Constant(Enum):
    NOT_APPLICABLE = "NOT_APPLICABLE"
    UNKNOWN = "Unknown"
    UNASSIGNED = "UNASSIGNED"
    STOREFRONT = "STOREFRONT"
    DEFAULT_SORTING_ORDER = 9
    BIG_SKU_SALES_THRESHOLD = 1_000
    BIG_SKU_FREE_UNITS_THRESHOLD = 1_000
    FUTURE_EVENT_PROMO_LENGTH = 10
    IN_APP_PURCHASE = "InAppPurchase"


class Origin(Enum):
    MAIN_STORE = "MAIN_STORE"
    RETAIL = "RETAIL"


class Boolean(Enum):
    """
    Boolean can be expressed as a True/False, Y/N, Yes/No etc.
    The Enum may evolve using next combinations when necessary.
    """

    TRUE = "True"
    FALSE = "False"


class StringLength(Enum):
    """Predefined lengths for various types of text data that
    can be present in reports. Should be used for validation checks
    that would verify if data doesn't excede expected length.

    For data which length is known beforehand (e.g. `country_code` etc.)
    exact length should be used in those checks.

    """

    TINY = 32
    SMALL = 255
    MEDIUM = 511
    HUGE = 1023


class RevenueFactor(Enum):
    """Predefined revenue factor used to calculate approximate net revenue, based on
    sales provision taken by portal:
    Epic: 12%
    All other portals: 30%
    """

    EPIC = 0.88
    STD = 0.7


PLATFORMID_PROMO_COOLDOWN_PERIOD = {
    "171010": {"2000-01-01": 42, "2022-03-28": 28, "2022-11-17": 30},
    "151511": {
        "2000-01-01": 21
    },  # the 28 is a placeholder, NOA can range from 7 to 21.
    "151512": {"2000-01-01": 28},
    "151513": {"2000-01-01": 27},
    "151514": {"2000-01-01": 28},
    "121010": {"2000-01-01": 56},
}

# need to convert to platfrom_region, nintendo doesn'thave the same split across regions
PLATFORM_PROMO_STRATEGY_DAYS = {
    "high_strategy": {
        "epic": 100,
        "gog": 60,
        "humble": 60,
        "meta": 60,
        "microsoft": 60,
        "nintendo": 100,
        "playsation": 50,
        "steam": 120,
    },
    "low_strategy": {
        "epic": 40,
        "gog": 0,
        "humble": 0,
        "meta": 0,
        "microsoft": 0,
        "nintendo": 50,
        "playsation": 0,
        "steam": 50,
    },
}

PORTAL_PLATFORM_REGION_ID_GAMES_WITH_BASE_PRICES = {
    171010,
    151511,
    151512,
    151513,
    151514,
}
