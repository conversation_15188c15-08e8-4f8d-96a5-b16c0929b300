import datetime
from enum import Enum
from statistics import mean

import numpy as np
import pandas as pd
import pandera as pa
import polars as pl

from core_silver.dictionaries.constants import (
    PORTAL_PLATFORM_REGION_ID_GAMES_WITH_BASE_PRICES,
    Constant,
)
from core_silver.legacy_aggregators.fact_sales.fact_sales import (
    run_aggregate_legacy_fact_sales,
)
from core_silver.legacy_aggregators.fact_sales.price_identifier import (
    add_country_currency_column,
)
from core_silver.observation_converter.pandas_utils import enforce_schema
from core_silver.utils.math_tools import js_round
from core_silver.validators.columns import (
    FloatInRange,
    HugeString,
    IntInRange,
    MediumString,
    NonNegativeFloat,
    NonNegativeInt,
    SmallString,
    TinyString,
)
from data_sdk.aggregator import BaseAggregator
from data_sdk.domain.constants import get_portal_platform_region_id
from data_sdk.domain.tables import (
    ExternalCountryCodesTable,
    LegacyFactEventDayTable,
    ObservationSalesTable,
    SilverPortalsTable,
    SilverSKUsTable,
)

event_day_schema = pa.DataFrameSchema(
    columns={
        "discount": NonNegativeFloat(),
        "date": TinyString(),
        "unique_sku_id": MediumString(),
        "portal_platform_region_id": IntInRange(101000, 999999),
        "studio_id": NonNegativeInt(),
        "date_from": TinyString(),
        "date_to": TinyString(),
        "event_day_number": FloatInRange(0, 1),
        "event_status": SmallString(),
        "event_id": MediumString(),
        "type": SmallString(),
        "event_name": MediumString(),
        "event_description": HugeString(nullable=True),
        "dates_short": SmallString(),
        "promo_length": NonNegativeInt(),
        "days_since_previous_discount": NonNegativeInt(),
    }
)

# TODO in input files for event_day
# rename ?? to  - human ... check ps_analytics_sku


class EventType(str, Enum):
    CALCULATED = "Calculated"
    USER_DEFINED = "User Defined"


class EventStatus(str, Enum):
    ONGOING = "ongoing"
    FINISHED = "finished"


class EventDayAggregator(BaseAggregator):
    table_cls = LegacyFactEventDayTable
    schema = event_day_schema

    def __init__(
        self,
        silver_skus: SilverSKUsTable,
        observation_sales: ObservationSalesTable,
        external_country_codes: ExternalCountryCodesTable,
        silver_portals: SilverPortalsTable,
    ) -> None:
        self._silver_skus = silver_skus
        self._observation_sales = observation_sales
        self._external_country_codes = external_country_codes
        self._silver_portals = silver_portals

    def _aggregate(self) -> pl.DataFrame:
        sku_df = self._silver_skus.df.to_pandas()
        silver_portals_df = self._silver_portals.df.to_pandas()
        country_codes_df = self._external_country_codes.df.to_pandas()

        fact_sales_df = run_aggregate_legacy_fact_sales(
            sku_df=sku_df,
            observations_df=self._observation_sales.df.to_pandas(),
            country_codes_df=country_codes_df,
        )

        return generate_fact_event_day(
            fact_sales_df=fact_sales_df,
            sku_df=sku_df,
            silver_portals_df=silver_portals_df,
            country_codes_df=country_codes_df,
        )


def generate_fact_event_day(
    fact_sales_df: pd.DataFrame,
    sku_df: pd.DataFrame,
    silver_portals_df: pd.DataFrame,
    country_codes_df: pd.DataFrame,
) -> pl.DataFrame:  # convert to polars df
    """This aggregator creates a table, which for every date_sku_studio with recognized discount, detects an event,
    and assign it to it.

    In the future, autodetected events would be merged with user-defined events here too, having same granulation.

    Args:
        aggregated (dict): Aggregated tables containing FACT_SALES aggregation

    Returns:
        DataFrame: Table with date_sku_studio assigned to events
    """
    if (
        silver_portals_df.empty
    ):  # TODO handle this case once empty df's contain proper schema
        return pl.DataFrame()
    silver_portals_df = silver_portals_df[["portal_platform_region", "store_name"]]
    silver_portals_df = silver_portals_df.rename(columns={"store_name": "store"})

    fact_sales_df = fact_sales_df.merge(
        silver_portals_df, how="left", on="portal_platform_region"
    )

    dim_sku_df = sku_df.rename(columns={"unique_sku_id": "sku_studio"})

    if fact_sales_df.empty:
        return create_empty_df()
    filtered_sales_df = _filter_most_important_sales_data(
        fact_sales_df, country_codes_df
    )
    all_discounts_df = _find_records_with_discount(filtered_sales_df)
    if all_discounts_df.empty:
        return create_empty_df()
    grouped_discounts_df = _find_date_ranges_for_discount_days(all_discounts_df)
    filtered_discounts_df = _filter_out_false_positives(grouped_discounts_df.copy())

    # at this point if grouped_discounts_df is empty we can return empty data frame
    # we make sure that it has all the necessary columns
    if filtered_discounts_df.empty:
        return create_empty_df()
    event_day_df = _prepare_final_event_day_table(
        filtered_discounts_df,
        dim_sku_df,
        fact_sales_df,
    )

    if event_day_df.empty:
        return create_empty_df()
    return pl.DataFrame(
        enforce_schema(
            event_day_df[
                event_day_df.columns.intersection(list(event_day_schema.columns.keys()))
            ],
            event_day_schema,
        ).sort_values(by=["unique_sku_id", "date"], ascending=False)
    )


def create_empty_df():
    return pl.DataFrame(pd.DataFrame(columns=event_day_schema.columns.keys()))


# TODO: check if can be reduced
def fill_missing_dates(group):
    expected_dates = pd.date_range(
        start=group["date_from"].iloc[0], end=group["date_to"].iloc[0]
    )
    group = (
        group.set_index("date")
        .reindex(expected_dates)
        .reset_index()
        .rename(columns={"index": "date"})
    )
    group["total_gross_sales"] = group["total_gross_sales"].fillna(0)
    return group.ffill()


def _filter_most_important_sales_data(
    fact_sales_df: pd.DataFrame, country_codes_df: pd.DataFrame
) -> pd.DataFrame:
    """Before we perform calculations, we would like to filter records that
    does not have price, sales or are bundles, so our calculations are as accurate as possible.
    We also would like to calculate discounts only for the biggest, most important countries.
    Every portal lets you set the discount only for the whole SKU, so we don't need to calculate
    discount for Serbia, because it is the same as in US and also having too small sample in Serbia
    would result in inaccurate numbers.


    Args:
        fact_sales_df (DataFrame): DataFrame with aggregated fact table Sales

    Returns:
        DataFrame: DF with data adequate to perform further calculations
    """
    fact_sales_df = fact_sales_df[
        (
            (fact_sales_df["price_local"] != 0)
            & (fact_sales_df["units_sold"] > 0)
            & (fact_sales_df["gross_sales"] > 0)
        )
    ]
    fact_sales_df: pd.DataFrame = fact_sales_df[
        (fact_sales_df["bundle_name"] == "Direct Package Sale")
        | (fact_sales_df["bundle_name"] == Constant.NOT_APPLICABLE.value)
    ]

    fact_sales_df = add_country_currency_column(fact_sales_df, country_codes_df)

    return fact_sales_df


def _find_records_with_discount(filtered_sales_df: pd.DataFrame) -> pd.DataFrame:
    """This foo calculates discount, filters the noise and returns only the discounts above 10%
    For one day there can be two discounts, for example sales on Steam ends at 10 A.M. so half of the
    day game is sold without discount. In such situation we take a discount value for the half of the day
    that generated more sales and this foo also does that.

    Args:
        filtered_sales_df (DataFrame): DF with data adequate to perform further calculations

    Returns:
        DataFrame: DF with date_sku_studio that have at least 10% discounts
    """
    all_discounts_df = pd.DataFrame().assign(
        date_sku_studio=filtered_sales_df["date_sku_studio"],
        gross_sales=filtered_sales_df["gross_sales"],
        date=filtered_sales_df["date"],
        sku_studio=filtered_sales_df["sku_studio"],
        discount=np.nan,
        studio_id=filtered_sales_df["studio_id"],
        unique_sku_id=filtered_sales_df["sku_studio"],
        currency_code=filtered_sales_df["currency_code"],
        country_currency=filtered_sales_df["country_currency"],
        price_local=filtered_sales_df["price_local"],
        calculated_base_price_local_v2=filtered_sales_df[
            "calculated_base_price_local_v2"
        ],
        calculated_base_price_usd_v2=filtered_sales_df["calculated_base_price_usd_v2"],
    )
    all_discounts_df["portal_platform_region_id"] = filtered_sales_df[
        "portal_platform_region"
    ].map(get_portal_platform_region_id)

    all_discounts_df["discount"] = js_round(
        (
            1
            - all_discounts_df["price_local"]
            / all_discounts_df["calculated_base_price_local_v2"]
        ),
        2,
    )

    all_discounts_df_currecny_filtered = all_discounts_df[
        all_discounts_df["currency_code"] == all_discounts_df["country_currency"]
    ]

    main_group_columns = [
        "date_sku_studio",
        "discount",
        "sku_studio",
        "date",
        "studio_id",
        "unique_sku_id",
        "portal_platform_region_id",
    ]

    # calculate gross sales for every found discount day
    all_discounts_df_grouped = (
        all_discounts_df_currecny_filtered[main_group_columns + ["gross_sales"]]
        .groupby(
            by=main_group_columns,
            as_index=False,
        )
        .sum()
        .rename(columns={"gross_sales": "total_gross_sales"})
    )

    cal_price_usd_with_highest_sales_df = (
        (
            all_discounts_df_currecny_filtered[
                ["sku_studio", "calculated_base_price_usd_v2", "gross_sales"]
            ]
            .groupby(
                by=["sku_studio", "calculated_base_price_usd_v2"],
                as_index=False,
            )
            .sum()
            .rename(columns={"gross_sales": "total_gross_sales_per_price"})
        )
        .sort_values(["sku_studio", "total_gross_sales_per_price"], ascending=False)
        .drop_duplicates("sku_studio")
    )

    all_discounts_df_grouped = all_discounts_df_grouped.merge(
        cal_price_usd_with_highest_sales_df[
            ["sku_studio", "calculated_base_price_usd_v2"]
        ],
        how="left",
    )

    # if there is more than one discount per day, find only the one with highest total_gross_sales

    filterded_discounts_df = all_discounts_df_grouped.sort_values(
        "total_gross_sales", ascending=False
    ).drop_duplicates(["date_sku_studio", "date", "sku_studio"])

    all_discounts_df = (
        all_discounts_df[["date_sku_studio", "gross_sales"]]
        .groupby(["date_sku_studio"])
        .sum()
        .rename(columns={"gross_sales": "total_gross_sales"})
        .reset_index()
    )
    filterded_discounts_df = filterded_discounts_df.drop(
        ["total_gross_sales"], axis=1
    ).merge(all_discounts_df[["date_sku_studio", "total_gross_sales"]])

    return filterded_discounts_df[filterded_discounts_df["discount"] >= 0.1]


def _find_date_ranges_for_discount_days(all_discounts: pd.DataFrame) -> pd.DataFrame:
    """Having single records with discounts, we need to find a range of an actual sale period.
    Then, every single record with discount should have the date_from and date_to of a period which it belongs to

    Args:
        all_discounts (DataFrame): DF with date_sku_studio that have at least 10% discounts

    Returns:
        DataFrame: DF with date_sku_studio that have at least 10% discounts and has date_from and date_to
    """
    # Sorts the records in a way that sales form a sequence of records
    grouped_discounts_df = all_discounts.sort_values(
        by=["sku_studio", "date"], ascending=[True, True]
    )
    grouped_discounts_df.date = pd.to_datetime(grouped_discounts_df.date).dt.date
    grouped_discounts_df["date_difference"] = grouped_discounts_df[
        "date"
    ] - grouped_discounts_df["date"].shift(1)

    mask = grouped_discounts_df["portal_platform_region_id"].isin([
        151511,
        151512,
        151513,
        151514,
    ])
    day_threshold = 2
    nintendo_day_threshold = 6
    grouped_discounts_df["sequence"] = 0

    nintendo_grouped_discounts_df = grouped_discounts_df[mask].copy()
    non_nintendo_grouped_discounts_df = grouped_discounts_df[~mask].copy()

    if not nintendo_grouped_discounts_df.empty:
        # Finds the end of every sequence AKA end of every sale and numerates the sales
        nintendo_grouped_discounts_df["sequence"] = (
            (
                (
                    nintendo_grouped_discounts_df["sku_studio"]
                    != nintendo_grouped_discounts_df["sku_studio"].shift(1)
                )
                | (
                    nintendo_grouped_discounts_df["date_difference"]
                    > datetime.timedelta(days=nintendo_day_threshold)
                )
            )
            .ne(0)
            .cumsum()
        )

        nintendo_grouped_discounts_df["discount"] = (
            nintendo_grouped_discounts_df.groupby(
                "sequence"
            )["discount"].transform(lambda x: x.value_counts().idxmax())
        )
        grouped_discounts_df.update(nintendo_grouped_discounts_df)

    if not non_nintendo_grouped_discounts_df.empty:
        non_nintendo_grouped_discounts_df["sequence"] = (
            (
                (
                    non_nintendo_grouped_discounts_df["sku_studio"]
                    != non_nintendo_grouped_discounts_df["sku_studio"].shift(1)
                )
                | (
                    abs(
                        non_nintendo_grouped_discounts_df["discount"]
                        - non_nintendo_grouped_discounts_df["discount"].shift(1)
                    )
                    >= 0.044
                )
                | (
                    non_nintendo_grouped_discounts_df["date_difference"]
                    > datetime.timedelta(days=day_threshold)
                )
            )
            .ne(0)
            .cumsum()
        )
        grouped_discounts_df.update(non_nintendo_grouped_discounts_df)

    # assigns date_from and date_to of a detected sale for every record
    grouped_discounts_df["date_from"] = (
        grouped_discounts_df[["sequence", "date"]]
        .groupby("sequence")["date"]
        .transform(min)
        .astype(str)
    )
    grouped_discounts_df["date_to"] = (
        grouped_discounts_df[["sequence", "date"]]
        .groupby("sequence")["date"]
        .transform(max)
        .astype(str)
    )

    grouped_discounts_df[["date", "date_from", "date_to"]] = grouped_discounts_df[
        ["date", "date_from", "date_to"]
    ].apply(pd.to_datetime)

    grouped_discounts_df = (
        grouped_discounts_df.groupby("sequence")
        .apply(fill_missing_dates)
        .reset_index(drop=True)
    )

    grouped_discounts_df[["date", "date_from", "date_to"]] = grouped_discounts_df[
        ["date", "date_from", "date_to"]
    ].apply(lambda x: x.dt.strftime("%Y-%m-%d"))

    grouped_discounts_df["promo_length"] = (
        grouped_discounts_df["date_to"].astype("datetime64[ns]")
        - grouped_discounts_df["date_from"].astype("datetime64[ns]")
    ).dt.days + 1

    return grouped_discounts_df


def _filter_out_false_positives(grouped_discounts_df: pd.DataFrame) -> pd.DataFrame:
    grouped_discounts_df["average_event_gross_sales"] = (
        grouped_discounts_df[["sequence", "total_gross_sales"]]
        .groupby("sequence")["total_gross_sales"]
        .transform(mean)
    )

    final_columns = [
        "date_sku_studio",
        "discount",
        "sku_studio",
        "date",
        "studio_id",
        "unique_sku_id",
        "portal_platform_region_id",
        "total_gross_sales",
        "sequence",
        "date_from",
        "date_to",
        "promo_length",
        "average_event_gross_sales",
    ]

    grouped_discounts_df["discounted_price_usd"] = (
        grouped_discounts_df["discount"]
        * grouped_discounts_df["calculated_base_price_usd_v2"]
    )
    games_with_base_price_mask = grouped_discounts_df["portal_platform_region_id"].isin(
        PORTAL_PLATFORM_REGION_ID_GAMES_WITH_BASE_PRICES
    )

    no_base_price_grouped_discounts_df = grouped_discounts_df.loc[
        ~games_with_base_price_mask
    ]

    # None steam
    acceptable_sales_volume = (
        no_base_price_grouped_discounts_df["average_event_gross_sales"] >= 200
    )
    suspicious_sales_volume = (
        no_base_price_grouped_discounts_df["average_event_gross_sales"] < 200
    ) & (no_base_price_grouped_discounts_df["average_event_gross_sales"] >= 20)

    suspicious_sales_volume_but_low_price = (
        (no_base_price_grouped_discounts_df["average_event_gross_sales"] < 200)
        & (no_base_price_grouped_discounts_df["average_event_gross_sales"] >= 4)
        & (no_base_price_grouped_discounts_df["discounted_price_usd"] <= 6)
    )

    acceptable_promo_length = (
        no_base_price_grouped_discounts_df["promo_length"] >= 3
    ) & (no_base_price_grouped_discounts_df["promo_length"] <= 29)
    suspicious_sales_volume_but_acceptable_promo_length = (
        suspicious_sales_volume & acceptable_promo_length
    )
    suspicious_sales_volume_but_low_price_and_acceptable_promo_length_and_low_price = (
        suspicious_sales_volume_but_low_price & acceptable_promo_length
    )

    no_base_price_grouped_discounts_df = no_base_price_grouped_discounts_df[
        acceptable_sales_volume
        | suspicious_sales_volume_but_acceptable_promo_length
        | suspicious_sales_volume_but_low_price_and_acceptable_promo_length_and_low_price
    ][final_columns]

    # Steam

    with_base_price_grouped_discounts_df = grouped_discounts_df.loc[
        (games_with_base_price_mask)
        & (
            (grouped_discounts_df["average_event_gross_sales"] >= 10)
            | (
                (grouped_discounts_df["promo_length"] >= 2)
                & (grouped_discounts_df["promo_length"] <= 32)
            )
        )
    ]

    grouped_discounts_df = pd.concat([
        no_base_price_grouped_discounts_df,
        with_base_price_grouped_discounts_df,
    ]).sort_values(["sku_studio", "date"])

    return grouped_discounts_df


def _prepare_event_names(
    converted_df: pd.DataFrame,
    grouped_discounts_df: pd.DataFrame,
    skus: pd.DataFrame,
) -> pd.DataFrame:
    """Prepare event names in the format: "PRODUCT_NAME STORE for N days", eg.:
    "SUPERHOT Steam for 4 days", if `product_name` is unassigned in the SKU table
    "UNASSIGNED" value is used.

    Args:
        converted_df: data frame with observations from raw reports
        grouped_discounts_df: data frame with grouped discounts
        skus: data frame with aggragated SKU data

    Returns:
        grouped discounts data frame with calculated event names

    """
    grouped_discounts_df.reset_index(drop=True, inplace=True)
    extended_discounts_df = grouped_discounts_df.merge(
        skus[["product_name", "sku_studio"]], how="left", on="sku_studio"
    ).merge(
        converted_df[["store", "sku_studio"]].drop_duplicates(),
        how="left",
        on="sku_studio",
    )

    grouped_discounts_df.loc[
        pd.isnull(extended_discounts_df["product_name"]), "event_name"
    ] = (
        Constant.UNASSIGNED.value
        + "\n"
        + extended_discounts_df.loc[
            pd.isnull(extended_discounts_df["product_name"]), "store"
        ]
        + " for "
        + extended_discounts_df.loc[
            pd.isnull(extended_discounts_df["product_name"]), "promo_length"
        ].astype(str)
    )
    grouped_discounts_df.loc[
        pd.notnull(extended_discounts_df["product_name"]), "event_name"
    ] = (
        extended_discounts_df.loc[
            pd.notnull(extended_discounts_df["product_name"]), "product_name"
        ]
        + "\n"
        + extended_discounts_df.loc[
            pd.notnull(extended_discounts_df["product_name"]), "store"
        ]
        + " for "
        + extended_discounts_df.loc[
            pd.notnull(extended_discounts_df["product_name"]), "promo_length"
        ].astype(str)
    )
    grouped_discounts_df.loc[
        (extended_discounts_df["promo_length"] > 1), "event_name"
    ] = (
        grouped_discounts_df.loc[
            (extended_discounts_df["promo_length"] > 1), "event_name"
        ]
        + " days"
    )
    grouped_discounts_df.loc[
        (extended_discounts_df["promo_length"] == 1), "event_name"
    ] = (
        grouped_discounts_df.loc[
            (extended_discounts_df["promo_length"] == 1), "event_name"
        ]
        + " day"
    )

    return grouped_discounts_df


def _compute_event_status(
    grouped_discounts_df: pd.DataFrame,
    fact_sales_df: pd.DataFrame,
) -> pd.DataFrame:
    """Compute `event_status` column based on sales table. Event is considered
    ongoing if for given `sku_studio` date of the last sale is equal to
    end date of the event.

    Args:
        grouped_discounts_df: data frame with grouped discounts
        fact_sales_df: data frame with sales data, needed for finding last sale date
                       for sku_studio
    Returns:
        DataFrame extended with `event_status` column

    """
    grouped_discounts_df["event_status"] = EventStatus.FINISHED.value
    sku_studio_last_date = fact_sales_df[["sku_studio", "date"]].drop_duplicates(
        "sku_studio", keep="last"
    )
    last_date_grouped_discounts_df = grouped_discounts_df[
        ["date_to", "sku_studio"]
    ].merge(sku_studio_last_date[["sku_studio", "date"]], how="left", on="sku_studio")
    grouped_discounts_df.loc[
        (
            last_date_grouped_discounts_df["date"]
            == last_date_grouped_discounts_df["date_to"]
        ),
        "event_status",
    ] = EventStatus.ONGOING.value
    return grouped_discounts_df


def _compute_days_since_prev_discount(
    grouped_discounts_df: pd.DataFrame,
) -> pd.Series:
    """Compute days since previous discount. Find the end of previous event
    end compute difference between this date and start of the current event.

    Args:
        grouped_discounts_df: data frame with grouped discounts

    Returns:
        Series with number of days since previous discount

    """
    grouped_event_sequences = grouped_discounts_df[
        ["sequence", "date_to", "sku_studio"]
    ].drop_duplicates()
    grouped_event_sequences.loc[1:, "date_to"] = (
        grouped_event_sequences["date_to"].shift(1).dropna()
    )
    grouped_event_sequences = grouped_discounts_df.merge(
        grouped_event_sequences,
        how="left",
        on=["sku_studio", "sequence"],
    )
    days_since_prev_discount = pd.Series(
        (
            grouped_event_sequences["date_from"].astype("datetime64[ns]")
            - grouped_event_sequences["date_to_y"].astype("datetime64[ns]")
        ).dt.days,
        dtype=np.int64,
    )
    # fix negative values which may appear for the very first event
    days_since_prev_discount.loc[(days_since_prev_discount < 0)] = 0
    return days_since_prev_discount


def _prepare_final_event_day_table(
    grouped_discounts_df: pd.DataFrame,
    skus: pd.DataFrame,
    fact_sales_df: pd.DataFrame,
) -> pd.DataFrame:
    """Drops columns that were needed only for calculations, fills additional
    columns like date_product_studio, names the events and sets the event type to Calculated

    Args:
        converted_df: data frame with observations from raw reports
        grouped_discounts_df: DF with date_sku_studio that have at least 10% discounts
                              and has date_from and date_to
        skus: data frame with aggregated SKU data
        fact_sales_df: data frame with  aggregated sales

    Returns:
        DataFrame: DF with Event day dimension schema
    """
    grouped_discounts_df["type"] = EventType.CALCULATED.value

    grouped_discounts_df = _prepare_event_names(
        fact_sales_df, grouped_discounts_df, skus
    )

    grouped_discounts_df["event_description"] = np.nan
    grouped_discounts_df = grouped_discounts_df.merge(
        skus[["product_name", "sku_studio"]], on="sku_studio", how="left"
    )
    grouped_discounts_df["product_name_normalized"] = Constant.UNASSIGNED.value
    grouped_discounts_df.loc[
        grouped_discounts_df["product_name"].notnull(), "product_name_normalized"
    ] = grouped_discounts_df["product_name"].astype(str)
    grouped_discounts_df["date_product_studio"] = (
        grouped_discounts_df["date"].astype(str)
        + ":"
        + grouped_discounts_df["product_name_normalized"]
        + ":"
        + grouped_discounts_df["studio_id"].astype(str)
    )

    grouped_discounts_df = _compute_event_status(grouped_discounts_df, fact_sales_df)

    grouped_discounts_df["dates_short"] = (
        grouped_discounts_df["date_from"]
        .astype("datetime64[ns]")
        .apply(lambda x: x.strftime("%b %d"))
    )
    grouped_discounts_df.loc[
        (grouped_discounts_df["event_status"] == EventStatus.ONGOING.value),
        "dates_short",
    ] = grouped_discounts_df["dates_short"] + " ongoing"
    grouped_discounts_df.loc[
        (grouped_discounts_df["event_status"] == EventStatus.FINISHED.value),
        "dates_short",
    ] = (
        grouped_discounts_df["dates_short"]
        + " "
        + grouped_discounts_df["date_to"]
        .astype("datetime64[ns]")
        .apply(lambda x: x.strftime("%b %d"))
    )

    grouped_discounts_df["days_since_previous_discount"] = (
        _compute_days_since_prev_discount(grouped_discounts_df)
    )
    grouped_discounts_df["event_day_number"] = (
        grouped_discounts_df["date"].astype("datetime64[ns]")
        - grouped_discounts_df["date_from"].astype("datetime64[ns]")
    ).dt.days
    grouped_discounts_df["event_day_number"] = js_round(
        grouped_discounts_df["event_day_number"]
        .div(grouped_discounts_df["promo_length"] - 1)
        .fillna(1.0),  # fix division by 0, which can happen, when promo_length == 1
        2,
    )

    grouped_discounts_df["event_id"] = (
        grouped_discounts_df["sku_studio"] + ":" + grouped_discounts_df["date_from"]
    )

    return grouped_discounts_df
