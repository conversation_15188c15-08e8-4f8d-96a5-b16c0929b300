import pandas as pd
import pandera as pa
import polars as pl

from core_silver.legacy_aggregators.common_aggregations import (
    compute_product_id_in_fact_aggregator,
)
from core_silver.legacy_aggregators.fact_sales.price_identifier import assign_new_price
from core_silver.legacy_aggregators.fact_sales.sales_utils import (
    calculate_base_price,
)
from core_silver.observation_converter.pandas_utils import enforce_schema
from core_silver.validators.columns import (
    ExactLengthString,
    FloatWithTwoDecimalPlaces,
    HashString,
    Int,
    IntInRange,
    MediumString,
    NonNegativeInt,
    NullableFloatWithTwoDecimalPlaces,
    SmallString,
    TinyString,
)
from data_sdk.aggregator import BaseAggregator
from data_sdk.domain.constants import get_portal_platform_region_id
from data_sdk.domain.tables import (
    ExternalCountryCodesTable,
    LegacyFactSalesTable,
    ObservationSalesTable,
    SilverSKUsTable,
)

fact_sales_schema = pa.DataFrameSchema(
    columns={
        "country_code": ExactLengthString(3),
        "currency_code": ExactLengthString(3),
        "studio_id": NonNegativeInt(),
        "sku_studio": MediumString(),
        # Legacy column name, observations use unique_sku_id
        "bundle_name": SmallString(),
        "portal_platform_region": MediumString(),
        "portal_platform_region_id": IntInRange(101000, 999999),
        "product_id": MediumString(),
        "hash_acquisition_properties": HashString(),
        "date": TinyString(),
        "date_sku_studio": MediumString(),
        "source_file_id": NonNegativeInt(),
        "retailer_tag": MediumString(),
        "base_price_local": NullableFloatWithTwoDecimalPlaces(),
        "calculated_base_price_usd": NullableFloatWithTwoDecimalPlaces(),
        "net_sales": FloatWithTwoDecimalPlaces(),
        "gross_returned": FloatWithTwoDecimalPlaces(),
        "gross_sales": FloatWithTwoDecimalPlaces(),
        "units_returned": Int(),
        "units_sold": Int(),
        "free_units": Int(),
        "price_local": FloatWithTwoDecimalPlaces(),
        "price_usd": FloatWithTwoDecimalPlaces(),
        "net_sales_approx": FloatWithTwoDecimalPlaces(),
        "category": SmallString(),
        "calculated_base_price_local_v2": NullableFloatWithTwoDecimalPlaces(),
        "calculated_base_price_usd_v2": NullableFloatWithTwoDecimalPlaces(),
    },
)


def run_aggregate_legacy_fact_sales(
    sku_df: pd.DataFrame,
    observations_df: pd.DataFrame,
    country_codes_df: pd.DataFrame,
) -> pd.DataFrame:
    if observations_df.empty:
        return _generate_empty_output(fact_sales_schema)

    fact_sales_df = pd.DataFrame()
    fact_sales_df = fact_sales_df.assign(
        base_price_local=observations_df["base_price_local"],
        bundle_name=observations_df["bundle_name"],
        category=observations_df["category"],
        country_code=observations_df["country_code"],
        currency_code=observations_df["currency_code"],
        date=observations_df["date"],
        date_sku_studio=observations_df["date"].astype(str)
        + ":"
        + observations_df["unique_sku_id"].astype(str),
        free_units=observations_df["free_units"],
        gross_returned=observations_df["gross_returned"],
        gross_sales=observations_df["gross_sales"],
        hash_acquisition_properties=observations_df["hash_acquisition_properties"],
        net_sales=observations_df["net_sales"],
        net_sales_approx=observations_df["net_sales_approx"],
        portal_platform_region=observations_df["portal_platform_region"],
        price_local=observations_df["price_local"],
        price_usd=observations_df["price_usd"],
        retailer_tag=observations_df["retailer_tag"],
        sku_studio=observations_df["unique_sku_id"],
        source_file_id=observations_df["report_id"],
        studio_id=observations_df["studio_id"],
        units_returned=observations_df["units_returned"],
        units_sold=observations_df["units_sold"],
        portal_platform_region_id=observations_df["portal_platform_region"].map(
            get_portal_platform_region_id
        ),
    )

    fact_sales_df["product_id"] = compute_product_id_in_fact_aggregator(
        fact_sales_df, sku_df
    )

    if not fact_sales_df.empty:
        df_base_price_calculations = calculate_base_price(fact_sales_df)

        fact_sales_df = FactSalesAggregator._merge_fact_sales_with_base_price_calc(
            fact_sales_df, df_base_price_calculations
        )
    else:
        fact_sales_df["calculated_base_price_usd"] = 0

    if fact_sales_df.empty:
        return _generate_empty_output(fact_sales_schema)

    new_prices_df = assign_new_price(fact_sales_df.drop_duplicates(), country_codes_df)
    new_prices_df["date"] = pd.to_datetime(new_prices_df["date"])
    fact_sales_df = fact_sales_df.merge(new_prices_df, how="left")

    fact_sales_df["date"] = fact_sales_df["date"].astype(str)
    return (
        enforce_schema(fact_sales_df, fact_sales_schema)
        .reindex(columns=fact_sales_schema.columns)
        .sort_values(by=["sku_studio", "date"], ascending=True)
    )


def _generate_empty_output(fact_sales_schema) -> pd.DataFrame:
    return enforce_schema(
        pd.DataFrame(columns=list(fact_sales_schema.columns.keys())),
        fact_sales_schema,
    )


class FactSalesAggregator(BaseAggregator):
    table_cls = LegacyFactSalesTable
    schema = fact_sales_schema

    def __init__(
        self,
        observation_sales: ObservationSalesTable,
        silver_skus: SilverSKUsTable,
        external_country_codes: ExternalCountryCodesTable,
    ) -> None:
        self._observation_sales = observation_sales
        self._silver_skus = silver_skus
        self._external_country_codes = external_country_codes

    @staticmethod
    def _merge_fact_sales_with_base_price_calc(
        fact_sales_df: pd.DataFrame, calculated_base_price_df: pd.DataFrame
    ) -> pd.DataFrame:
        return pd.merge(
            fact_sales_df,
            calculated_base_price_df,
            on=[
                "sku_studio",
                "country_code",
                "currency_code",
            ],
            how="left",
        )

    def _aggregate(self) -> pl.DataFrame:
        return pl.DataFrame(
            run_aggregate_legacy_fact_sales(
                sku_df=self._silver_skus.df.to_pandas(),
                observations_df=self._observation_sales.df.to_pandas(),
                country_codes_df=self._external_country_codes.df.to_pandas(),
            )
        )
