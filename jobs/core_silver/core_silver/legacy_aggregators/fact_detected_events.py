import math

import numpy as np
import pandas as pd
import pandera as pa
import polars as pl
from pandas import DateOffset

from core_silver.legacy_aggregators.fact_event_day import generate_fact_event_day
from core_silver.legacy_aggregators.fact_sales.fact_sales import (
    run_aggregate_legacy_fact_sales,
)
from core_silver.observation_converter.pandas_utils import enforce_schema
from core_silver.validators.columns import (
    Int,
    MediumString,
    NonNegativeFloat,
    NonNegativeInt,
    TinyString,
)
from data_sdk.aggregator import BaseAggregator
from data_sdk.domain import DisplayPortal
from data_sdk.domain.constants import get_portal_platform_region_id
from data_sdk.domain.platform import DisplayPlatform
from data_sdk.domain.regions import Region
from data_sdk.domain.tables import (
    ExternalCountryCodesTable,
    ExternalSKUsTable,
    ExternalSteamEventsTable,
    LegacyFactDetectedEventsTable,
    ObservationSalesTable,
    SilverPortalsTable,
    SilverSKUsTable,
)

PLATFORMID_PROMO_COOLDOWN_PERIOD = {
    "171010": {"2000-01-01": 42, "2022-03-28": 28, "2022-11-17": 30},
    "151511": {
        "2000-01-01": 21
    },  # the 28 is a placeholder, NOA can range from 7 to 21.
    "151512": {"2000-01-01": 28},
    "151513": {"2000-01-01": 27},
    "151514": {"2000-01-01": 28},
    "121010": {"2000-01-01": 56},
}


# TODO: utils?
def get_release_date_and_last_date(df: pd.DataFrame, drop=None) -> pd.DataFrame:
    #  Fetching categorical sku_studio somehow ignores gross sales and units sold filter
    df["sku_studio"] = df["sku_studio"].astype(str)

    release_date_and_last_date_df = (
        df[(df["gross_sales"] > 0) & (df["units_sold"] > 0)][["sku_studio", "date"]]
        .groupby(["sku_studio"])
        .agg({"date": ["max", "min"]})
        .reset_index()
    )
    release_date_and_last_date_df.columns = [
        "sku_studio",
        "last_sales_date",
        "release_date",
    ]
    release_date_and_last_date_df["last_sales_date"] = release_date_and_last_date_df[
        "last_sales_date"
    ].astype(str)
    release_date_and_last_date_df["release_date"] = release_date_and_last_date_df[
        "release_date"
    ].astype(str)
    release_date_and_last_date_df["unique_sku_id"] = release_date_and_last_date_df[
        "sku_studio"
    ]
    if drop:
        release_date_and_last_date_df = release_date_and_last_date_df.drop(drop, axis=1)
    return release_date_and_last_date_df


steam_ppr_id = get_portal_platform_region_id(
    DisplayPortal.STEAM.value
    + ":"
    + DisplayPlatform.PC.value
    + ":"
    + Region.GLOBAL.value
)
steam_ppr_ids = {steam_ppr_id}

noa_ppr_id = get_portal_platform_region_id(
    DisplayPortal.NINTENDO.value
    + ":"
    + DisplayPlatform.SWITCH.value
    + ":"
    + Region.NINTENDO_AMERICA.value
)
noe_ppr_id = get_portal_platform_region_id(
    DisplayPortal.NINTENDO.value
    + ":"
    + DisplayPlatform.SWITCH.value
    + ":"
    + Region.NINTENDO_EUROPE.value
)
nas_ppr_id = get_portal_platform_region_id(
    DisplayPortal.NINTENDO.value
    + ":"
    + DisplayPlatform.SWITCH.value
    + ":"
    + Region.NINTENDO_ASIA.value
)
nja_ppr_id = get_portal_platform_region_id(
    DisplayPortal.NINTENDO.value
    + ":"
    + DisplayPlatform.SWITCH.value
    + ":"
    + Region.NINTENDO_JAPAN.value
)
ncl_ppr_ids = {nas_ppr_id, nja_ppr_id}
nintendo_ppr_ids = {noa_ppr_id, noe_ppr_id, nas_ppr_id, nja_ppr_id}

DEFAULT_EVENT_NAME = "unused"
DEFAULT_EVENT_TYPE = "unused"


class FactDetectedEventsAggregator(BaseAggregator):
    table_cls = LegacyFactDetectedEventsTable

    def __init__(
        self,
        silver_skus: SilverSKUsTable,
        observation_sales: ObservationSalesTable,
        external_country_codes: ExternalCountryCodesTable,
        silver_portals: SilverPortalsTable,
        external_skus: ExternalSKUsTable,
        external_steam_events: ExternalSteamEventsTable,
    ) -> None:
        self._silver_skus = silver_skus
        self._observation_sales = observation_sales
        self._external_country_codes = external_country_codes
        self._silver_portals = silver_portals
        self._external_skus = external_skus
        self._external_steam_events = external_steam_events

    schema = pa.DataFrameSchema(
        columns={
            # # Output Features
            "unique_sku_id": MediumString(),
            "studio_id": NonNegativeInt(),
            "release_date": TinyString(),
            "last_sales_date": TinyString(),
            "discount_depth": NonNegativeFloat(nullable=True),
            "discount_type": MediumString(nullable=True),
            "date_from": TinyString(),
            "date_to": TinyString(),
            "major": NonNegativeInt(),
            "event_name": MediumString(),
            "portal_platform_region_id": NonNegativeInt(),
            "base_sku_id": MediumString(),
            "human_name": MediumString(),
            "product_name": MediumString(nullable=True),
            "gso": Int(),
            "event_id": MediumString(),
            # new columns
            "event_type": MediumString(),
            "promo_length": NonNegativeFloat(nullable=True),
        },
    )

    def _aggregate(self) -> pl.DataFrame:
        sku_df = self._silver_skus.df.to_pandas()
        observations_df = self._observation_sales.df.to_pandas()
        silver_portals_df = self._silver_portals.df.to_pandas()
        country_codes_df = self._external_country_codes.df.to_pandas()

        fact_sales_df = run_aggregate_legacy_fact_sales(
            sku_df=sku_df,
            observations_df=observations_df,
            country_codes_df=country_codes_df,
        )

        fact_event_day_df = generate_fact_event_day(
            fact_sales_df=fact_sales_df,
            sku_df=sku_df,
            silver_portals_df=silver_portals_df,
            country_codes_df=country_codes_df,
        ).to_pandas()

        if observations_df.empty | sku_df.empty | fact_event_day_df.empty:
            return self._generate_empty_result()

        # The model only detects events for Steam or Nintendo sales
        observations_df = observations_df[
            observations_df["portal"].isin([
                DisplayPortal.STEAM,
                DisplayPortal.NINTENDO,
            ])
        ]
        if observations_df.empty:
            return self._generate_empty_result()

        external_steam_events_df = self._external_steam_events.df.to_pandas()
        observations_df["portal_platform_region_id"] = (
            observations_df["portal_platform_region"]
            .astype(str)
            .map(get_portal_platform_region_id)
        )
        observations_df = observations_df.rename(
            columns={"unique_sku_id": "sku_studio"}
        )
        dim_sku_df = sku_df.drop(columns=["release_date"])
        dim_sku_df = dim_sku_df.rename(columns={"unique_sku_id": "sku_studio"})
        dim_sku_df["gso"] = _calculate_gso(dim_sku_df, observations_df)
        dim_sku_df["portal_platform_region_id"] = dim_sku_df[
            "portal_platform_region"
        ].map(get_portal_platform_region_id)

        # filtering for the portals and platforms of interest
        fact_event_day_df = fact_event_day_df[
            fact_event_day_df["portal_platform_region_id"].isin(
                steam_ppr_ids.union(nintendo_ppr_ids)
            )
        ]
        observations_df = observations_df[
            observations_df["portal_platform_region_id"].isin(
                steam_ppr_ids.union(nintendo_ppr_ids)
            )
        ]

        if fact_event_day_df.empty | observations_df.empty:
            return self._generate_empty_result()

        release_date_and_last_date_df = get_release_date_and_last_date(observations_df)
        dim_sku_df, eligible_sku_list = _get_sku_list_from_dim_sku(
            dim_sku_df,
            release_date_and_last_date_df,
            good_ppr_ids=steam_ppr_ids.union(nintendo_ppr_ids),
        )

        fact_event_day_df = _prepare_fact_event_day(
            fact_event_day_df, eligible_sku_list
        )

        if fact_event_day_df.empty:
            return self._generate_empty_result()

        steam_events_calendar = _get_steam_events_history(external_steam_events_df)
        steam_events_full = _prepare_steam_events_history(steam_events_calendar)

        fact_event_day_combined = _combine_fact_event_steam_event(
            fact_event_day_df, steam_events_full, steam_ppr_ids
        )

        # separating Steam events from non-Steam events
        fact_event_day_steam = fact_event_day_combined[
            fact_event_day_combined["portal_platform_region_id"].isin(steam_ppr_ids)
        ].reset_index(drop=True)
        fact_event_day_resid = fact_event_day_combined[
            ~fact_event_day_combined["portal_platform_region_id"].isin(steam_ppr_ids)
        ].reset_index(drop=True)
        # we 'feed' only the Steam part in the function
        events_steam = _seperate_transform_events(fact_event_day_steam)
        events_resid = fact_event_day_resid.drop_duplicates(
            subset=["unique_sku_id", "date_from"], keep="first", ignore_index=True
        )[
            [
                "unique_sku_id",
                "date_from",
                "date_to",
                "promo_length",
                "discount_depth",
                "portal_platform_region_id",
                "major",
                "event_name",
            ]
        ].copy()

        all_events_df, start_timeline, end_timeline = _set_timeline(
            events_steam,
            events_resid,
            release_date_and_last_date_df,
            steam_events_calendar,
        )

        daily_grid = _create_daily_grid(
            all_events_df, release_date_and_last_date_df, start_timeline, end_timeline
        )

        detected_steam = _transform_daily_grid_to_events(
            daily_grid[
                daily_grid["portal_platform_region_id"].isin(steam_ppr_ids)
            ].reset_index(drop=True)
        )
        detected_nintendo_europe = _transform_daily_grid_to_events(
            daily_grid[
                daily_grid["portal_platform_region_id"] == noe_ppr_id
            ].reset_index(drop=True)
        )
        detected_nintendo_asia = _transform_daily_grid_to_events(
            daily_grid[
                daily_grid["portal_platform_region_id"].isin(ncl_ppr_ids)
            ].reset_index(drop=True)
        )
        detected_nintendo_north_america = (
            _calculate_periods_daily_nintendo_north_america(
                daily_grid[
                    daily_grid["portal_platform_region_id"] == noa_ppr_id
                ].reset_index(drop=True)
            )
        )

        detected_events: pd.DataFrame = pd.concat(
            [
                detected_steam,
                detected_nintendo_north_america,
                detected_nintendo_europe,
                detected_nintendo_asia,
            ],
            ignore_index=True,
        )

        if detected_events.empty:
            return pl.DataFrame(pd.DataFrame(columns=self.schema.columns.keys()))

        detected_events = _create_final_features_detected_events(detected_events)

        detected_events = detected_events.merge(
            dim_sku_df[
                [
                    "studio_id",
                    "unique_sku_id",
                    "base_sku_id",
                    "human_name",
                    "product_name",
                    "gso",
                    "release_date",
                    "last_sales_date",
                ]
            ],
            on="unique_sku_id",
            how="left",
        )

        return pl.DataFrame(enforce_schema(detected_events, self.schema))

    def _generate_empty_result(self) -> pl.DataFrame:
        return pl.DataFrame(pd.DataFrame(columns=self.schema.columns.keys()))


def _get_sku_list_from_dim_sku(
    dim_sku_df: pd.DataFrame,
    release_date_and_last_date_df: pd.DataFrame,
    good_ppr_ids=set(),
):
    if "unique_sku_id" not in dim_sku_df.columns:
        dim_sku_df = dim_sku_df.rename(columns={"sku_studio": "unique_sku_id"})
    dim_sku_df["human_name_clean"] = (
        dim_sku_df["human_name"].str.lower().str.replace(r"\W|_| ", "", regex=True)
    )

    dim_sku_df = dim_sku_df[
        (dim_sku_df["gso"] > 0)
        & (~dim_sku_df["human_name_clean"].str.contains("commerciallicense"))
        & (dim_sku_df["sku_type"] == "SALES")
    ]
    dim_sku_df = pd.merge(
        dim_sku_df, release_date_and_last_date_df, on="unique_sku_id", how="left"
    )

    dim_sku_df["base_sku_id_ints"] = pd.to_numeric(
        dim_sku_df["base_sku_id"], errors="coerce"
    )
    dim_sku_df = (
        dim_sku_df[dim_sku_df["portal_platform_region_id"].isin(good_ppr_ids)]
        .sort_values(
            by=[
                "portal_platform_region_id",
                "studio_id",
                "human_name",
                "release_date",
                "last_sales_date",
                "base_sku_id_ints",
            ],
            ascending=True,
            na_position="first",
            ignore_index=True,
        )
        .drop_duplicates(
            subset=["portal_platform_region_id", "studio_id", "human_name"], keep="last"
        )
    )

    return dim_sku_df, dim_sku_df["unique_sku_id"].unique().tolist()


def _prepare_fact_event_day(fact_event_day_df, eligible_sku_list):
    fact_event_day_df["date"] = fact_event_day_df["date"].astype(str)
    fact_event_day_df["discount_depth"] = (100 * fact_event_day_df["discount"]).astype(
        int
    )
    fact_event_day_df[
        ["promo_length", "days_since_previous_discount", "portal_platform_region_id"]
    ] = fact_event_day_df[
        ["promo_length", "days_since_previous_discount", "portal_platform_region_id"]
    ].astype(int)
    fact_event_day_df = fact_event_day_df[
        fact_event_day_df["unique_sku_id"].isin(eligible_sku_list)
    ]
    return fact_event_day_df.reset_index(drop=True)


def _get_steam_events_history(external_steam_events_df: pd.DataFrame):
    return external_steam_events_df.sort_values(
        by="start_date", ascending=True, ignore_index=True
    )


def _prepare_steam_events_history(steam_events_calendar):
    steam_events_calendar["date"] = [
        pd.date_range(start=row["start_date"], end=row["end_date"], freq="D")
        .strftime("%Y-%m-%d")
        .to_list()
        for _, row in steam_events_calendar.iterrows()
    ]

    steam_events_full = steam_events_calendar.explode("date", ignore_index=True)
    steam_events_full = (
        steam_events_full.sort_values(
            by=["date", "major", "start_date"], ascending=True
        )
        .drop_duplicates(subset=["date"], keep="last", ignore_index=True)
        .copy()
    )

    return steam_events_full


def _combine_fact_event_steam_event(
    fact_event_day_df: pd.DataFrame, steam_events_full: pd.DataFrame, major_ppr_ids: set
):
    """major_ppr_ids: the ppor IDs for which colum major remains unchanged
    (for all other ppr IDs column 'major' receives the value 0)"""
    fact_event_day_df = (
        pd.merge(
            fact_event_day_df[
                [
                    "unique_sku_id",
                    "studio_id",
                    "date",
                    "portal_platform_region_id",
                    "date_from",
                    "date_to",
                    "promo_length",
                    "discount_depth",
                ]
            ],
            steam_events_full[["date", "major", "name"]],
            on="date",
            how="left",
        )
        .fillna(value={"major": 0})
        .rename(columns={"name": "event_name"})
    )
    fact_event_day_df["major"] = np.where(
        fact_event_day_df["portal_platform_region_id"].isin(major_ppr_ids),
        fact_event_day_df["major"],
        0,
    ).astype(int)

    fact_event_day_df["event_name"] = np.where(
        fact_event_day_df["major"] == 0, "Custom Sale", fact_event_day_df["event_name"]
    )

    return fact_event_day_df.sort_values(
        by=["unique_sku_id", "date"], ascending=True, ignore_index=True
    )


def _seperate_transform_events(fact_event_day_combined):
    """returns a dataframe on a periods level"""

    fact_event_day_combined[["prev_date", "prev_major"]] = (
        fact_event_day_combined.groupby("unique_sku_id")[["date", "major"]]
        .shift(1)
        .fillna(value={"date": "2000-01-01", "major": -1})
        .astype({"date": str, "major": int})
    )

    fact_event_day_combined["delta_days"] = (
        pd.to_datetime(fact_event_day_combined["date"])
        - pd.to_datetime(fact_event_day_combined["prev_date"])
    ).dt.days

    fact_event_day_combined["new_event"] = (
        fact_event_day_combined["delta_days"] > 1
    ) | (fact_event_day_combined["major"] != fact_event_day_combined["prev_major"])

    fact_event_day_combined["event_number"] = fact_event_day_combined.groupby(
        "unique_sku_id"
    )["new_event"].cumsum()

    correct_dates_from_to = (
        fact_event_day_combined.groupby(by=["unique_sku_id", "event_number"])
        .agg(
            date_from=("date", "min"),
            date_to=("date", "max"),
            promo_length=("date", "count"),
        )
        .reset_index()
    )

    events_df = (
        fact_event_day_combined[
            [
                "studio_id",
                "unique_sku_id",
                "event_number",
                "date",
                "portal_platform_region_id",
                "discount_depth",
                "major",
                "event_name",
            ]
        ]
        .merge(correct_dates_from_to, on=["unique_sku_id", "event_number"])
        .drop_duplicates(subset=["unique_sku_id", "event_number"], ignore_index=True)
        .drop(columns=["date"])
    )

    return events_df


def _set_timeline(
    events_steam: pd.DataFrame,
    events_resid: pd.DataFrame,
    release_date_and_last_date_df: pd.DataFrame,
    steam_events_calendar: pd.DataFrame,
):
    release_date = release_date_and_last_date_df["release_date"].min()
    start_timeline = pd.to_datetime(release_date)
    end_timeline = pd.Timestamp.today().date() + DateOffset(months=12)

    # No sale for steam yet
    # we filter for Steam because this date slices future_steam_major
    last_sales_date = release_date_and_last_date_df[
        release_date_and_last_date_df["unique_sku_id"]
        .astype(str)
        .str.contains("-steam:", regex=False)
    ]["last_sales_date"].max()
    # When processing Nintendo sales, Steam sales are not available
    if isinstance(last_sales_date, float) and math.isnan(last_sales_date):
        last_sales_date = release_date

    future_steam_major = steam_events_calendar[
        (
            pd.to_datetime(steam_events_calendar["end_date"])
            > pd.to_datetime(last_sales_date)
        )
        & (steam_events_calendar["major"] == 1)
    ].rename(
        columns={
            "start_date": "date_from",
            "end_date": "date_to",
            "name": "event_name",
        }
    )[["date_from", "date_to", "major", "event_name"]]

    future_steam_major = future_steam_major.assign(
        discount_depth=np.nan, portal_platform_region_id=steam_ppr_id
    )

    skus_max_date = (
        events_steam.groupby("unique_sku_id")["date_to"]
        .max()
        .to_frame()
        .rename(columns={"date_to": "max_date_to"})
        .reset_index()
    )
    future_steam_major = pd.merge(skus_max_date, future_steam_major, how="cross")
    future_steam_major["date_from"] = np.maximum(
        pd.to_datetime(future_steam_major["max_date_to"], format="%Y-%m-%d")
        + pd.Timedelta(1, unit="D"),
        pd.to_datetime(future_steam_major["date_from"], format="%Y-%m-%d"),
    ).dt.strftime("%Y-%m-%d")
    all_events_df = pd.concat(
        [events_steam, future_steam_major.drop(columns=["max_date_to"])],
        ignore_index=True,
    )
    all_events_df["date_to"] = pd.to_datetime(all_events_df["date_to"])
    all_events_df = (
        all_events_df[all_events_df["date_to"] >= start_timeline]
        .reset_index(drop=True)
        .copy()
    )

    all_events_df = pd.concat([all_events_df, events_resid], ignore_index=True)

    all_events_df["event_type"] = np.where(
        all_events_df["event_name"] == "Custom Sale", "discount", "store_discount"
    )

    all_events_df["date"] = [
        pd.date_range(
            start=row["date_from"], end=row["date_to"], freq="D", inclusive="both"
        )
        .strftime("%Y-%m-%d")
        .to_list()
        for _, row in all_events_df.iterrows()
    ]

    return all_events_df, start_timeline, end_timeline


def _create_daily_grid(
    all_events_df: pd.DataFrame,
    release_date_and_last_date_df: pd.DataFrame,
    start_timeline,
    end_timeline,
) -> pd.DataFrame:
    sku_list = release_date_and_last_date_df[
        release_date_and_last_date_df["unique_sku_id"].isin(
            set(all_events_df["unique_sku_id"])
        )
        & (
            pd.to_datetime(release_date_and_last_date_df["last_sales_date"])
            >= start_timeline
        )
    ]["unique_sku_id"].to_list()

    all_dates = (
        pd.date_range(start=start_timeline, end=end_timeline, freq="D")
        .strftime("%Y-%m-%d")
        .to_list()
    )

    daily_grid = pd.DataFrame({"unique_sku_id": sku_list}).merge(
        pd.DataFrame({"date": all_dates}), how="cross"
    )

    daily_grid = daily_grid.merge(
        release_date_and_last_date_df[
            ["unique_sku_id", "release_date", "last_sales_date"]
        ],
        on="unique_sku_id",
        how="left",
    )

    daily_grid["start_date"] = daily_grid["date"] >= daily_grid["release_date"]
    daily_grid = daily_grid[daily_grid["start_date"]].reset_index(drop=True)

    daily_grid = daily_grid[["unique_sku_id", "date", "start_date"]].merge(
        all_events_df.explode("date")[
            [
                "unique_sku_id",
                "date",
                "major",
                "event_name",
                "event_type",
                "promo_length",
                "discount_depth",
            ]
        ],
        on=["unique_sku_id", "date"],
        how="left",
    )
    daily_grid["major"] = daily_grid["major"].fillna(0).astype(int)

    daily_grid = daily_grid.sort_values(
        by=["unique_sku_id", "date"], ascending=True, ignore_index=True
    )

    # attaching the portal-platform-region ID for each SKU (this is needed to determine the function/parameters that will handle the calculation of periods):
    # attached via a separate merge, because otherwise there will be NAs on regular days
    daily_grid = pd.merge(
        daily_grid,
        all_events_df.drop_duplicates(subset=["unique_sku_id"], keep="first")[
            ["unique_sku_id", "portal_platform_region_id"]
        ],
        on="unique_sku_id",
    )

    return daily_grid


def _transform_daily_grid_to_events(
    daily_grid: pd.DataFrame,
) -> pd.DataFrame:
    if daily_grid.empty:
        return pd.DataFrame()

    daily_grid["event_name"].fillna(value=DEFAULT_EVENT_NAME, inplace=True)
    daily_grid["event_type"].fillna(value=DEFAULT_EVENT_TYPE, inplace=True)

    cooldown_data = []
    for platform, dates in PLATFORMID_PROMO_COOLDOWN_PERIOD.items():
        for date, length in dates.items():
            cooldown_data.append([platform, pd.to_datetime(date), length])

    cooldown_df = pd.DataFrame(
        cooldown_data, columns=["portal_platform_region_id", "date", "cooldown_length"]
    ).sort_values(by=["portal_platform_region_id", "date"])

    daily_grid["portal_platform_region_id"] = daily_grid[
        "portal_platform_region_id"
    ].astype(str)

    daily_grid["date"] = pd.to_datetime(daily_grid["date"])
    merged_df = pd.merge_asof(
        daily_grid.sort_values("date"),
        cooldown_df,
        by="portal_platform_region_id",
        on="date",
        direction="backward",
    )

    daily_grid = daily_grid.sort_values(
        by=["unique_sku_id", "date"], ascending=True, ignore_index=True
    )
    merged_df = merged_df.sort_values(
        by=["unique_sku_id", "date"], ascending=True, ignore_index=True
    )

    # Select and rename the columns as needed
    daily_grid["cooldown_length"] = merged_df["cooldown_length"]

    # If there are rows without a match in cooldown_df, set their cooldown_length to None
    daily_grid["cooldown_length"].fillna(np.nan, inplace=True)

    daily_grid["date"] = daily_grid["date"].astype(str)

    daily_grid["temp_col"] = 1
    daily_grid = _add_cooldown_to_grid(daily_grid, event_type="post")
    daily_grid = _add_cooldown_to_grid(daily_grid, event_type="pre")
    daily_grid["cooldown"] = daily_grid["pre_cooldown"] | daily_grid["post_cooldown"]

    daily_grid = daily_grid.sort_values(
        by=["unique_sku_id", "date"], ascending=True, ignore_index=True
    )
    daily_grid["event_name"] = np.where(
        (daily_grid["event_name"] == DEFAULT_EVENT_NAME) & daily_grid["cooldown"],
        "cooldown",
        daily_grid["event_name"],
    )
    daily_grid["event_type"] = np.where(
        (daily_grid["event_type"] == DEFAULT_EVENT_TYPE) & daily_grid["cooldown"],
        "cooldown",
        daily_grid["event_type"],
    )

    daily_grid["event_number"] = daily_grid["event_type"] != daily_grid.groupby(
        "unique_sku_id"
    )["event_type"].shift(1)
    daily_grid["event_number"] = daily_grid.groupby("unique_sku_id")[
        "event_number"
    ].cumsum()

    detected_events = (
        daily_grid.groupby(by=["unique_sku_id", "event_number"])
        .agg(
            date_from=("date", "min"),
            date_to=("date", "max"),
            major=("major", "first"),
            event_name=("event_name", "first"),
            event_type=("event_type", "first"),
            promo_length=("promo_length", "first"),
            discount_depth=("discount_depth", "first"),
            portal_platform_region_id=("portal_platform_region_id", "first"),
        )
        .reset_index()
    )

    return detected_events


def _add_cooldown_to_grid(daily_grid, event_type="post"):
    if event_type == "post":
        ascending = True
        cooldown_column = event_type + "_cooldown"
    else:
        ascending = False
        cooldown_column = event_type + "_cooldown"

    daily_grid = daily_grid.sort_values(
        by=["unique_sku_id", "date"], ascending=ascending, ignore_index=True
    )
    daily_grid["prev_event_name"] = daily_grid.groupby("unique_sku_id")[
        "event_name"
    ].shift(1)
    daily_grid["start_cooldown"] = (daily_grid["prev_event_name"] == "Custom Sale") & (
        daily_grid["event_name"] != daily_grid["prev_event_name"]
    )
    daily_grid["start_cooldown"] = daily_grid.groupby("unique_sku_id")[
        "start_cooldown"
    ].cumsum()
    daily_grid["cooldown"] = daily_grid.groupby(by=["unique_sku_id", "start_cooldown"])[
        "temp_col"
    ].cumsum()
    daily_grid[cooldown_column] = (
        (daily_grid["cooldown"] <= daily_grid["cooldown_length"])
        & (daily_grid["start_cooldown"] > 0)
        & (daily_grid["event_name"] != "Custom Sale")
    )

    return daily_grid


def _calculate_periods_daily_nintendo_north_america(
    daily_grid: pd.DataFrame, min_cooldown=7
):
    """This function calculates cooldown and unused/available days on a daily basis for Nintendo America.
    The dataframe must have columns 'unique_sku_id', 'date', 'event_type' and 'promo_length'
    with only a single row for each combination of 'unique_sku_id' and 'date'.
    For regular days there should be a missing value in column 'promo_length'.
    There should be no missing days, i.e. for each SKU all days between the min and max date
    for that SKU should be present.
    Varialble 'default_name' provides the string used as the 'event name' for days on which the SKU
    was neither on discount nor on cooldown.
    The 'date' column must be in pandas datetime format.
    The dataframe must be sorted in based on 'unique_sku_id' (any order) and 'date' (ascending)
    """

    if daily_grid.empty:
        return pd.DataFrame()

    daily_grid["event_name"].fillna(value=DEFAULT_EVENT_NAME, inplace=True)
    daily_grid["event_type"].fillna(value=DEFAULT_EVENT_TYPE, inplace=True)

    daily_grid["detected_discount"] = ~daily_grid["promo_length"].isna()
    daily_grid["regular_day"] = daily_grid["promo_length"].isna()
    daily_grid["last_day"] = (
        daily_grid.groupby("unique_sku_id")["detected_discount"]
        .shift(-1)
        .fillna(value=False)
        & daily_grid["regular_day"]
    )

    daily_grid["cooldown_pre"] = (
        1 * daily_grid["regular_day"]
        - 1 * daily_grid["last_day"]
        - np.maximum(
            min_cooldown, daily_grid.groupby("unique_sku_id")["promo_length"].shift(-1)
        ).fillna(value=0)
    )
    daily_grid.sort_values(
        by=["unique_sku_id", "date"], ascending=False, ignore_index=True, inplace=True
    )
    daily_grid["last_day"] = daily_grid.groupby("unique_sku_id")["last_day"].cumsum()
    daily_grid["cooldown_pre"] = (
        daily_grid.groupby(by=["unique_sku_id", "last_day"])["cooldown_pre"].transform(
            lambda x: x.cumsum() < 0
        )
        & daily_grid["regular_day"]
    )

    daily_grid.sort_values(
        by=["unique_sku_id", "date"], ascending=True, ignore_index=True, inplace=True
    )
    daily_grid["first_day"] = (
        daily_grid.groupby("unique_sku_id")["detected_discount"]
        .shift(1)
        .fillna(value=False)
        & daily_grid["regular_day"]
    )
    daily_grid["cooldown_post"] = (
        1 * daily_grid["regular_day"]
        - 1 * daily_grid["first_day"]
        - np.maximum(
            min_cooldown,
            np.ceil(daily_grid.groupby("unique_sku_id")["promo_length"].shift(1) / 2),
        ).fillna(value=0)
    )
    daily_grid["first_day"] = daily_grid.groupby("unique_sku_id")["first_day"].cumsum()
    daily_grid["cooldown_post"] = (
        daily_grid.groupby(by=["unique_sku_id", "first_day"])[
            "cooldown_post"
        ].transform(lambda x: x.cumsum() < 0)
        & daily_grid["regular_day"]
    )

    # unifying the cooldown
    daily_grid["cooldown"] = daily_grid["cooldown_pre"] | daily_grid["cooldown_post"]

    daily_grid["event_name"] = np.where(
        (daily_grid["event_name"] == DEFAULT_EVENT_NAME) & daily_grid["cooldown"],
        "cooldown",
        daily_grid["event_name"],
    )
    daily_grid["event_type"] = np.where(
        (daily_grid["event_type"] == DEFAULT_EVENT_TYPE) & daily_grid["cooldown"],
        "cooldown",
        daily_grid["event_type"],
    )

    daily_grid["event_number"] = daily_grid["event_type"] != daily_grid.groupby(
        "unique_sku_id"
    )["event_type"].shift(1)
    daily_grid["event_number"] = daily_grid.groupby("unique_sku_id")[
        "event_number"
    ].cumsum()

    detected_events = (
        daily_grid.groupby(by=["unique_sku_id", "event_number"])
        .agg(
            date_from=("date", "min"),
            date_to=("date", "max"),
            major=("major", "first"),
            event_name=("event_name", "first"),
            event_type=("event_type", "first"),
            promo_length=("promo_length", "first"),
            discount_depth=("discount_depth", "first"),
            portal_platform_region_id=("portal_platform_region_id", "first"),
        )
        .reset_index()
    )

    return detected_events


def _create_final_features_detected_events(detected_events):
    detected_events["discount_type"] = np.select(
        [
            detected_events["event_type"] == "store_discount",
            detected_events["event_type"] == "discount",
        ],
        ["store", "custom"],
        default=None,
    )

    detected_events["event_id"] = (
        detected_events["unique_sku_id"]
        + ":"
        + detected_events["date_from"].astype(str)
    )

    return detected_events.drop(columns=["event_number"])


# TODO its in dim_sku, maybe utils?
def _calculate_gso(skus_df: pd.DataFrame, sales_df: pd.DataFrame) -> pd.Series:
    # we only need aggregated when we aggregate sales SKUs
    # so when we get passed aggregated we expect fact sales table
    # to be there
    # it would be better if we had access to observation type here
    grouped_sales_df = (
        sales_df[["sku_studio", "gross_sales"]]
        .groupby(["sku_studio"], as_index=False)
        .sum()
    )
    result = _aggregate_gross_sales(skus_df, grouped_sales_df)
    result.fillna(0, inplace=True)
    return result


# TODO up
def _aggregate_gross_sales(
    sku_df: pd.DataFrame, grouped_sales_df: pd.DataFrame
) -> pd.Series:
    sku_df = sku_df.merge(
        grouped_sales_df[["gross_sales", "sku_studio"]],
        on=["sku_studio"],
        how="left",
    )
    return sku_df["gross_sales"]
