import pandas as pd
import pandera as pa
import polars as pl

from core_silver.dictionaries.constants import Constant
from core_silver.legacy_aggregators.common_aggregations import (
    concatenate_product_id,
)
from core_silver.observation_converter.pandas_utils import enforce_schema
from core_silver.validators.columns import (
    Bool,
    FloatInRange,
    Int,
    IntInRange,
    MediumString,
    SmallString,
)
from data_sdk.aggregator import BaseAggregator
from data_sdk.domain.constants import get_portal_platform_region_id
from data_sdk.domain.tables import (
    LegacyDimSKUsTable,
    ObservationSalesTable,
    SilverSKUsTable,
)


class DimSkuAggregator(BaseAggregator):
    table_cls = LegacyDimSKUsTable

    def __init__(
        self, silver_skus: SilverSKUsTable, observation_sales: ObservationSalesTable
    ) -> None:
        self._silver_skus = silver_skus
        self._observation_sales = observation_sales

    schema = pa.DataFrameSchema(
        columns={
            "base_sku_id": SmallString(),
            "human_name": MediumString(),
            "store_id": SmallString(),
            "studio_id": Int(),
            "portal_platform_region": MediumString(),
            "human_name_indicator": SmallString(),
            "sku_type": SmallString(),
            "product_name": MediumString(nullable=True),
            "product_type": SmallString(nullable=True),
            "sku_studio": MediumString(),
            "portal_platform_region_id": IntInRange(101000, 999999),
            "product_id": MediumString(),
            "package_name": MediumString(nullable=True),
            "custom_group": MediumString(nullable=True),
            "ratio": FloatInRange(0, 1),
            "gso": Int(),
            "is_baseline_precalculated": Bool(),
        }
    )

    def _aggregate(self) -> pl.DataFrame:
        silver_skus_df = self._silver_skus.df.to_pandas()
        silver_sales_df = self._observation_sales.df.to_pandas()
        if silver_skus_df.empty:
            return pl.DataFrame()
        silver_skus_df["portal_platform_region_id"] = silver_skus_df[
            "portal_platform_region"
        ].apply(get_portal_platform_region_id)
        silver_skus_df["product_id"] = concatenate_product_id(silver_skus_df)
        silver_skus_df["package_name"] = None
        silver_skus_df["custom_group"] = None
        silver_skus_df["ratio"] = 1.0
        silver_skus_df["gso"] = _calculate_gso(silver_skus_df, silver_sales_df)
        silver_skus_df = _add_baseline_calc_status(silver_skus_df)
        # Keep the legacy name of unique_sku_id column
        silver_skus_df = silver_skus_df.rename(columns={"unique_sku_id": "sku_studio"})
        silver_skus_df = silver_skus_df[self.schema.columns.keys()]
        return pl.DataFrame(enforce_schema(silver_skus_df, self.schema))


def _calculate_gso(skus_df: pd.DataFrame, sales_df: pd.DataFrame) -> pd.Series:
    # we only need aggregated when we aggregate sales SKUs
    # so when we get passed aggregated we expect fact sales table
    # to be there
    # it would be better if we had access to observation type here
    if sales_df.empty:
        return pd.Series(index=skus_df.index, dtype=float, data=0.0)
    else:
        grouped_sales_df = (
            sales_df[["unique_sku_id", "gross_sales"]]
            .groupby(["unique_sku_id"], as_index=False)
            .sum()
        )
        sku_df = skus_df.merge(
            grouped_sales_df[["gross_sales", "unique_sku_id"]],
            on=["unique_sku_id"],
            how="left",
        )
        return sku_df["gross_sales"].fillna(0)


def _add_baseline_calc_status(skus_with_gso: pd.DataFrame) -> pd.DataFrame:
    skus_with_gso["is_baseline_precalculated"] = False
    skus_with_gso.loc[
        skus_with_gso["gso"] > Constant.BIG_SKU_SALES_THRESHOLD.value,
        "is_baseline_precalculated",
    ] = True
    return skus_with_gso
