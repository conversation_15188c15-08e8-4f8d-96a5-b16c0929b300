import pandas as pd
import polars as pl

from core_silver.legacy_aggregators.common_aggregations import (
    compute_date_product_studio,
    compute_product_id_in_fact_aggregator,
)
from core_silver.observation_converter.pandas_utils import enforce_schema
from core_silver.validators.fields import (
    BasicField,
    HashStringField,
    MediumStringField,
    NonNegativeNumberField,
    SmallStringField,
    TinyStringField,
)
from data_sdk.aggregator import BaseAggregator, StrictBaseSchema
from data_sdk.domain.constants import get_portal_platform_region_id
from data_sdk.domain.tables import (
    LegacyFactVisibilityTable,
    ObservationVisibilityTable,
    SilverSKUsTable,
)


class FactVisibilitySchema(StrictBaseSchema):
    portal_platform_region: str = MediumStringField()
    portal_platform_region_id: int = BasicField(ge=101000, le=999999)
    product_id: str = MediumStringField()
    sku_studio: str = MediumStringField()
    # Legacy column name, observations use unique_sku_id
    date: str = TinyStringField()
    studio_id: int = NonNegativeNumberField()
    date_sku_studio: str = MediumStringField()
    hash_traffic_source: str = HashStringField()
    date_product_studio: str = MediumStringField()
    visits: int = BasicField()
    owner_visits: int = BasicField()
    impressions: int = BasicField()
    owner_impressions: int = BasicField()
    navigation: str = SmallStringField()


class FactVisibilityAggregator(BaseAggregator):
    table_cls = LegacyFactVisibilityTable
    schema = FactVisibilitySchema

    def __init__(
        self,
        silver_skus: SilverSKUsTable,
        observation_visibility: ObservationVisibilityTable,
    ):
        self._silver_skus = silver_skus
        self._observation_visibility = observation_visibility

    def _aggregate(self) -> pl.DataFrame:
        observations_df = self._observation_visibility.df.to_pandas()
        sku_df = self._silver_skus.df.to_pandas()

        if observations_df.empty:
            return self._generate_empty_output()

        fact_visibility_df = pd.DataFrame()
        fact_visibility_df = fact_visibility_df.assign(
            date=observations_df["date"],
            date_sku_studio=observations_df["date"].astype(str)
            + ":"
            + observations_df["unique_sku_id"].astype(str),
            hash_traffic_source=observations_df["hash_traffic_source"].astype(str),
            impressions=observations_df["impressions"],
            navigation=observations_df["navigation"],
            owner_impressions=observations_df["owner_impressions"],
            owner_visits=observations_df["owner_visits"],
            portal_platform_region=observations_df["portal_platform_region"].astype(
                str
            ),
            sku_studio=observations_df["unique_sku_id"].astype(str),
            studio_id=observations_df["studio_id"],
            visits=observations_df["visits"],
            portal_platform_region_id=observations_df["portal_platform_region"]
            .astype(str)
            .map(get_portal_platform_region_id),
        )
        fact_visibility_df["date_product_studio"] = compute_date_product_studio(
            fact_visibility_df, sku_df
        )
        fact_visibility_df["product_id"] = compute_product_id_in_fact_aggregator(
            fact_visibility_df, sku_df
        )
        fact_visibility_df["date"] = fact_visibility_df["date"].astype(str)
        fact_visibility_df = pl.DataFrame(
            fact_visibility_df.reindex(
                columns=self.schema.to_schema().columns
            ).sort_values(
                by=["sku_studio", "date"],
                ascending=False,
            )
        )
        self.schema.to_schema().validate(fact_visibility_df)
        return fact_visibility_df

    def _generate_empty_output(self) -> pl.DataFrame:
        return pl.DataFrame(
            enforce_schema(
                pd.DataFrame(columns=list(self.schema.to_schema().columns)),
                self.schema.to_schema(),
            )
        )
