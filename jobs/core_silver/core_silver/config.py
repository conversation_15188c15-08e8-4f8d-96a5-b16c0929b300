from pathlib import Path
from typing import Literal

from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict

from core_silver.external_sources.connectors.report_service import (
    APIConfig,
    StaticConfig,
)
from data_sdk.config import DLSConfig, DummyConfig, LocalConfig


class LocalRawFilesReader(BaseSettings):
    reader_type: Literal["local"] = "local"
    source_dir: Path


class BloblRawFilesReader(BaseSettings):
    reader_type: Literal["blob"] = "blob"
    account_name: str
    container_name: str


class Config(BaseSettings):
    model_config = SettingsConfigDict(env_nested_delimiter="__")

    hostname: str = "localhost"
    job_name: str = "core-silver-job"

    env: str = "local"
    app_version: str = "1.0.0"
    service_name: str = "core-silver-job"

    docker_tag: str = ""
    docker_build_timestamp: str = ""

    crawled_public_data_cfg: DLSConfig | LocalConfig = Field(
        discriminator="type",
        default=LocalConfig(local_dir=Path("playground/public-data-crawlers")),
    )
    input_cfg: DLSConfig | LocalConfig = Field(
        discriminator="type",
        default=LocalConfig(local_dir=Path("playground/raw")),
    )
    converted_reports_cfg: DLSConfig | DummyConfig | LocalConfig = Field(
        discriminator="type",
        default=LocalConfig(local_dir=Path("playground/converted")),
    )
    output_cfg: DLSConfig | DummyConfig | LocalConfig = Field(
        discriminator="type",
        default=LocalConfig(local_dir=Path("playground/result")),
    )

    report_service: StaticConfig | APIConfig = Field(
        discriminator="type",
        default=StaticConfig(),
    )

    user_service: StaticConfig | APIConfig = Field(
        discriminator="type",
        default=StaticConfig(),
    )
