from zipfile import BadZipFile

import numpy as np
import pandas as pd
import pandera as pa
from pandera import Column

from core_silver.dictionaries.constants import (
    <PERSON><PERSON>an,
    Constant,
    Origin,
    RevenueFactor,
)
from core_silver.dictionaries.vat import convert_net_to_gross
from core_silver.observation_converter.common_columns import (
    generate_portal_platform_region,
)
from core_silver.observation_converter.converters.base_converter import (
    BaseSalesConverter,
    extract_csvs_from_raw_file,
    get_manifest_data_from_raw_file,
)
from core_silver.observation_converter.converters.exceptions import (
    FileExtractionError,
    FileSchemaError,
    InvalidManifest,
    MetaFileNameWithoutPlatform,
)
from core_silver.observation_converter.pandas_utils import InvalidSchema, enforce_schema
from core_silver.utils.math_tools import js_round
from core_silver.utils.string_format import translate_column, unescape_html
from data_sdk.domain import DisplayPortal, Portal, countries
from data_sdk.domain.platform import (
    DisplayPlatform,
    Platform,
    get_display_platform_from_platform,
)
from data_sdk.domain.regions import Region
from data_sdk.domain.stores import Store


class MetaConverter(BaseSalesConverter):
    _schema = pa.DataFrameSchema(
        columns={
            "Time (PT)": Column(pa.DateTime, coerce=True),
            "Country": Column(pa.String, coerce=True),
            "Revenue": Column(pa.Float, nullable=True, coerce=True),
            "Copies Sold": Column(pa.Int, nullable=True, coerce=True),
            "Active Users": Column(pa.Int, nullable=True, coerce=True),
            "File Name": Column(pa.String, coerce=True),
        }
    )

    def _extract_file(self, raw_file: bytes) -> pd.DataFrame:
        try:
            self.manifest_from_file = get_manifest_data_from_raw_file(raw_file)
            results = extract_csvs_from_raw_file(
                raw_file,
                df_transform=self._extract_file_name_info,
                sep=",",
                names=[
                    "Time (PT)",
                    "Country",
                    "Revenue",
                    "Copies Sold",
                    "Active Users",
                ],
                header=0,
                skiprows=0,
                parse_dates=True,
                quotechar='"',
                keep_default_na=False,
                na_values=[""],
                dtype={
                    "Time (PT)": str,
                    "Country": str,
                    "Revenue": str,
                    "Copies Sold": np.float64,  # ensures null and numeric values support in raw file
                    "Active Users": np.float64,  # ensures null and numeric values support in raw file
                    "File Name": str,
                },
            )
        except (ValueError, BadZipFile) as ex:
            raise FileExtractionError(str(ex))

        return results

    def _parse(self, raw_file: bytes) -> pd.DataFrame:
        df = self._extract_file(raw_file)
        df.sort_index(ascending=True, inplace=True)
        df = df[df["Revenue"].notna() | df["Copies Sold"].notna()]
        df["Revenue"] = df["Revenue"].replace({r"\$": "", ",": ""}, regex=True)
        df = df[(df["Revenue"] != 0) & (df["Copies Sold"] != 0)]
        df.loc[df["Active Users"].isnull(), "Active Users"] = 0
        try:
            return enforce_schema(df, self._schema)
        except InvalidSchema as ex:
            raise FileSchemaError(str(ex))

    def _human_name_from_manifest(self, file_name: str) -> str:
        try:
            return self.manifest_from_file["metadata"]["fileMetaData"][file_name][
                "humanName"
            ]
        except KeyError:
            raise InvalidManifest(message="Product not found in manifest")

    def _platform_from_file_name(self, file_name: str) -> str:
        rift = ["OCULUS_RIFT", "META_RIFT"]
        quest = ["META_QUEST", "OCULUS_QUEST"]
        if any(platform in file_name.upper() for platform in quest):
            return Platform.QUEST.value
        elif any(platform in file_name.upper() for platform in rift):
            return Platform.RIFT.value
        else:
            raise MetaFileNameWithoutPlatform

    def _extract_file_name_info(
        self, data_frame: pd.DataFrame, file_name: str
    ) -> pd.DataFrame:
        data_frame["File Name"] = file_name
        data_frame["Platform"] = self._platform_from_file_name(file_name)
        data_frame["Human Name"] = self._human_name_from_manifest(file_name)
        return data_frame

    def _convert(self) -> pd.DataFrame:
        parsed_df = self._parse(self._raw_report.raw_file)
        metadata = self._raw_report.metadata  # TODO rename metadata

        if parsed_df.empty:
            return pd.DataFrame()
        converted_df = pd.DataFrame()
        converted_df = converted_df.assign(
            country_code=parsed_df["Country"].map(countries.get_country_alpha_3),
            currency_code="USD",
            portal=DisplayPortal.META,
            bundle_name=Constant.NOT_APPLICABLE.value,
            platform=parsed_df["Platform"].map(get_display_platform_from_platform),
            transaction_type=Constant.NOT_APPLICABLE.value,
            payment_instrument=Constant.UNKNOWN.value,
            tax_type=Constant.UNKNOWN.value,
            sale_modificator=Constant.NOT_APPLICABLE.value,
            iap_flag=Boolean.FALSE.value,
            date=parsed_df["Time (PT)"].map(lambda x: x.strftime("%Y-%m-%d")),
            retailer_tag=Constant.NOT_APPLICABLE.value,
            human_name=translate_column(parsed_df["Human Name"], unescape_html),
            region=Region.GLOBAL.value,
            studio_id=metadata.studio_id,
            price_local=0,
            base_price_local=np.nan,
            units_returned=0,
            report_id=metadata.report_id,
            acquisition_origin=Origin.MAIN_STORE.value,
            units_sold=parsed_df["Copies Sold"],
            net_sales=js_round(parsed_df["Revenue"], 2),
            free_units=0,
            price_usd=0.0,
            net_sales_approx=0.0,
            gross_returned=0.0,
            sku_id=parsed_df["Human Name"].replace({r"\s": "_", r"\W": ""}, regex=True),
        )

        converted_df["acquisition_platform"] = parsed_df["Platform"]

        converted_df.loc[converted_df["net_sales"].isna(), "net_sales"] = 0
        converted_df.loc[converted_df["net_sales"] == 0, "free_units"] = converted_df[
            "units_sold"
        ]
        converted_df.loc[
            (converted_df["net_sales"] == 0) & (converted_df["free_units"] > 0),
            "units_sold",
        ] = 0

        # TODO: IS THIS CORRECT? It was hardcoded in the original code, but why it is like that?
        # We have "country_code" column in df, so why we don't use it?
        hardcoded_country_code = "USA"

        converted_df["gross_sales"] = convert_net_to_gross(
            converted_df["net_sales"], hardcoded_country_code
        )

        converted_df["store_id"] = converted_df["sku_id"]

        converted_df.loc[
            (converted_df["net_sales"] > 0) & (converted_df["units_sold"] != 0),
            "price_local",
        ] = js_round(converted_df["gross_sales"] / converted_df["units_sold"], 2)
        converted_df["price_usd"] = converted_df["price_local"]

        converted_df["unique_sku_id"] = (
            converted_df["sku_id"].astype(str)
            + "-"
            + parsed_df["Platform"].astype(str)
            + "-"
            + Portal.META.value
            + ":"
            + converted_df["studio_id"].astype(str)
        )

        converted_df["store"] = np.where(
            converted_df["platform"] == DisplayPlatform.RIFT.value,
            Store.META_RIFT.value,
            Store.META_QUEST.value,
        )

        converted_df["abbreviated_name"] = converted_df[["store"]].apply(
            lambda c: Store(c["store"]).abbreviate(), axis=1
        )

        converted_df["net_sales_approx"] = js_round(
            converted_df["net_sales"] * RevenueFactor.STD.value, 2
        )
        converted_df["portal_platform_region"] = generate_portal_platform_region(
            converted_df["portal"], converted_df["platform"], converted_df["region"]
        )

        return converted_df
