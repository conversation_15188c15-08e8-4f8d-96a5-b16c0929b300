import io
from zipfile import BadZipFile, ZipFile

import numpy as np
import pandas as pd
import pandera as pa
from pandera import Column

from core_silver.dictionaries.constants import <PERSON><PERSON><PERSON>, Constant, RevenueFactor
from core_silver.observation_converter.common_columns import (
    generate_portal_platform_region,
)
from core_silver.observation_converter.converters.base_converter import (
    BaseSalesConverter,
    extract_csvs_from_raw_file,
    extract_json_from_raw_zip_file,
)
from core_silver.observation_converter.converters.exceptions import (
    FileExtractionError,
    FileSchemaError,
    PSNonMigratedRegionError,
)
from core_silver.observation_converter.converters.playstation_utils import (
    assign_playstation_store_information,
    normalize_dataframe,
)
from core_silver.observation_converter.pandas_utils import InvalidSchema, enforce_schema
from core_silver.observation_converter.zip import get_report_names, is_zip
from core_silver.utils.math_tools import js_round
from core_silver.utils.string_format import translate_column, unescape_html
from data_sdk.domain import DisplayPortal, Portal, countries, regions
from data_sdk.domain.platform import get_display_platform_from_platform

FREE_SPECIFIC_VALUE = "Non-billable"


def _transform(df) -> pd.DataFrame:
    # Change headers from PS_Analytics report generated before 2021-10-06
    # to its current version
    if "Region" in df:
        df = df.rename(columns={"Region": "SIE Region"})
    if "Country" in df:
        df = df.rename(columns={"Country": "Country/Region"})
    return df


class PlaystationConverter(BaseSalesConverter):
    _schema = pa.DataFrameSchema(
        columns={
            "Date": Column(pa.DateTime, coerce=True),
            "Partner Name": Column(pa.String, coerce=True),
            "Franchise": Column(pa.String, coerce=True),
            "Concept ID": Column(pa.Int, coerce=True),
            "Concept": Column(pa.String, coerce=True),
            "Product ID": Column(pa.String, coerce=True),
            "Product Name": Column(pa.String, coerce=True),
            "Product Name (Long)": Column(pa.String, coerce=True),
            "Product SKU ID": Column(pa.String, coerce=True),
            "Product SKU": Column(pa.String, coerce=True),
            "Product Primary Class": Column(pa.String, coerce=True),
            "Product Secondary Class": Column(pa.String, coerce=True),
            "Product Tertiary Class": Column(pa.String, coerce=True),
            "Storefront": Column(pa.String, coerce=True),
            "Billable Type": Column(pa.String, coerce=True),
            "Pre Order Indicator": Column(pa.String, coerce=True),
            "Consumable Indicator": Column(pa.String, coerce=True),
            "In Game Indicator": Column(pa.String, coerce=True),
            "In Game Source Title ID": Column(pa.String, coerce=True),
            "In Game Title Name": Column(pa.String, coerce=True),
            "Content Type": Column(pa.String, coerce=True),
            "Title Platform": Column(pa.String, coerce=True),
            "Platform": Column(pa.String, coerce=True),
            "SIE Region": Column(pa.String, coerce=True),
            "Country Code": Column(pa.String, coerce=True),
            "Country/Region": Column(pa.String, coerce=True),
            "PS Plus Indicator": Column(pa.String, coerce=True),
            "Service Discount %": Column(pa.Int, coerce=True),
            "Local Currency": Column(pa.String, coerce=True),
            "Sales Incl Tax Local": Column(pa.Float, coerce=True),
            "Sales Exc Tax Local": Column(pa.Float, coerce=True),
            "Sales Incl Tax $": Column(pa.Float, coerce=True),
            "Sales Exc Tax $": Column(pa.Float, coerce=True),
            "Sales Quantity": Column(pa.Int, coerce=True),
        }
    )

    def _parse(self, raw_file: bytes) -> pd.DataFrame:
        df = self._extract_file(raw_file)
        df = _transform(df)
        df.sort_index(ascending=True, inplace=True)

        if df.loc[
            (df["SIE Region"].str.startswith("Switch to SIE Region")), "SIE Region"
        ].count():
            raise PSNonMigratedRegionError

        try:
            return enforce_schema(df, self._schema)
        except InvalidSchema as ex:
            raise FileSchemaError(str(ex))

    def _convert(self) -> pd.DataFrame:
        parsed_df = self._parse(self._raw_report.raw_file)
        metadata = self._raw_report.metadata

        if parsed_df.empty:
            return pd.DataFrame()
        converted_df = pd.DataFrame()
        converted_df = converted_df.assign(
            country_code=parsed_df["Country Code"].map(countries.get_country_alpha_3),
            currency_code=parsed_df["Local Currency"],
            sku_id=parsed_df["Product SKU ID"],
            bundle_name=Constant.NOT_APPLICABLE.value,
            portal=DisplayPortal.PLAYSTATION,
            platform=parsed_df["Content Type"].map(get_display_platform_from_platform),
            human_name=translate_column(
                parsed_df["Product Name (Long)"], unescape_html
            ),
            transaction_type=self._calculate_transaction_type(parsed_df),
            payment_instrument=Constant.UNKNOWN.value,
            tax_type=Constant.UNKNOWN.value,
            sale_modificator=parsed_df["PS Plus Indicator"].astype(str)
            + "-"
            + parsed_df["Service Discount %"].astype(str),
            acquisition_platform=Constant.STOREFRONT.value,
            acquisition_origin=parsed_df["Storefront"],
            iap_flag=Boolean.FALSE.value,
            date=parsed_df["Date"].map(lambda x: x.strftime("%Y-%m-%d")),
            retailer_tag=Constant.NOT_APPLICABLE.value,
            store_id=parsed_df["Product ID"],
            region=parsed_df["SIE Region"],
            studio_id=metadata.studio_id,
            base_price_local=np.nan,
            report_id=metadata.report_id,
            gross_sales=js_round(parsed_df["Sales Incl Tax $"], 2),
            net_sales=js_round(parsed_df["Sales Exc Tax $"], 2),
            units_sold=0,
            units_returned=0,
            free_units=0,
            price_local=0.0,
            price_usd=0.0,
            net_sales_approx=0.0,
            gross_returned=0,
        )
        # Support handling free items without Local Currency provided in raw file
        converted_df.loc[
            (parsed_df["Local Currency"] == "N/A")
            & (parsed_df["Sales Incl Tax Local"] == 0),
            "currency_code",
        ] = "USD"
        converted_df.loc[parsed_df["In Game Indicator"] != "N", "iap_flag"] = (
            Boolean.FALSE.value
        )
        converted_df.loc[converted_df["gross_sales"] < 0, "gross_returned"] = js_round(
            -converted_df["gross_sales"], 2
        )
        converted_df.loc[
            parsed_df["Billable Type"] != FREE_SPECIFIC_VALUE, "units_sold"
        ] = parsed_df["Sales Quantity"]
        converted_df.loc[
            converted_df["units_sold"] < 0, "units_returned"
        ] = -converted_df["units_sold"]
        converted_df.loc[
            parsed_df["Billable Type"] == FREE_SPECIFIC_VALUE, "free_units"
        ] = parsed_df["Sales Quantity"]
        converted_df.loc[converted_df["units_sold"] != 0, "price_local"] = js_round(
            parsed_df["Sales Incl Tax Local"] / converted_df["units_sold"], 2
        )
        converted_df.loc[converted_df["units_sold"] != 0, "price_usd"] = js_round(
            converted_df["gross_sales"] / converted_df["units_sold"], 2
        )
        converted_df["unique_sku_id"] = (
            converted_df["sku_id"].astype(str)
            + "-"
            + converted_df["region"].astype(str)
            + "-"
            + Portal.PLAYSTATION.value
            + ":"
            + converted_df["studio_id"].astype(str)
        )
        converted_df["region"] = converted_df["region"].map(
            regions.get_ps_region_by_country_alpha_3
        )

        converted_df = assign_playstation_store_information(converted_df)

        converted_df["net_sales_approx"] = js_round(
            converted_df["net_sales"] * RevenueFactor.STD.value, 2
        )
        converted_df["portal_platform_region"] = generate_portal_platform_region(
            converted_df["portal"], converted_df["platform"], converted_df["region"]
        )

        return converted_df

    def _calculate_transaction_type(self, parsed_df) -> pd.Series:
        temp_df = pd.DataFrame({
            "pre_order_str": parsed_df["Pre Order Indicator"].map({
                "Y": "Pre Order;",
                "N": "",
            }),
            "in_game_str": parsed_df["In Game Indicator"].map({
                "Y": "In Game;",
                "N": "",
            }),
            "consumable_str": parsed_df["Consumable Indicator"].map({
                "Y": "Consumable;",
                "N": "",
            }),
        })

        result = (
            temp_df["pre_order_str"]
            + temp_df["in_game_str"]
            + temp_df["consumable_str"]
        ).str.rstrip(";")

        result.loc[result == ""] = Constant.UNKNOWN.value

        return result

    def _load_csvs_to_df(self, raw_file: bytes) -> pd.DataFrame:
        try:
            df = extract_csvs_from_raw_file(
                raw_file,
                sep=",",
                header=0,
                skiprows=0,
                parse_dates=True,
                quotechar='"',
                keep_default_na=False,
                na_values=[""],
                dtype={
                    "Date": str,
                    "Partner Name": str,
                    "Franchise": str,
                    "Concept ID": np.int64,
                    "Concept": str,
                    "Product ID": str,
                    "Product Name": str,
                    "Product Name (Long)": str,
                    "Product SKU ID": str,
                    "Product SKU": str,
                    "Product Primary Class": str,
                    "Product Secondary Class": str,
                    "Product Tertiary Class": str,
                    "Storefront": str,
                    "Billable Type": str,
                    "Pre Order Indicator": str,
                    "Consumable Indicator": str,
                    "In Game Indicator": str,
                    "In Game Source Title ID": str,
                    "In Game Title Name": str,
                    "Content Type": str,
                    "Title Platform": str,
                    "Platform": str,
                    "SIE Region": str,
                    "Country Code": str,
                    "Country/Region": str,
                    "PS Plus Indicator": str,
                    "Service Discount %": np.int64,
                    "Local Currency": str,
                    "Sales Incl Tax Local": np.float64,
                    "Sales Exc Tax Local": np.float64,
                    "Sales Incl Tax $": np.float64,
                    "Sales Exc Tax $": np.float64,
                    "Sales Quantity": np.int64,
                },
            )
        except ValueError as ex:
            raise FileExtractionError(str(ex))
        return df

    def _load_jsons_to_df(self, raw_file: bytes) -> pd.DataFrame:
        try:
            df = extract_json_from_raw_zip_file(raw_file, orient="index")
        except BadZipFile as ex:
            raise FileExtractionError(str(ex))
        return normalize_dataframe(df)

    def _extract_file(self, raw_file: bytes) -> pd.DataFrame:
        with io.BytesIO(raw_file) as stream:
            if is_zip(raw_file):
                zip_file = ZipFile(stream)
                report_names = get_report_names(zip_file)
                if all(name.endswith(".json") for name in report_names):
                    # zip with JSON - PS API
                    return self._load_jsons_to_df(raw_file)
            # single CSV - PS Analytics
            return self._load_csvs_to_df(raw_file)
