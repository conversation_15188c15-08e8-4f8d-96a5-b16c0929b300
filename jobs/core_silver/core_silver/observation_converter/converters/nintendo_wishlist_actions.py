from datetime import datetime
from zipfile import BadZipFile

import numpy as np
import pandas as pd
import pandera as pa
from pandera import Column

from core_silver.observation_converter.common_columns import (
    generate_portal_platform_region,
)
from core_silver.observation_converter.converters.base_converter import (
    BaseWishlistActionsConverter,
    extract_csvs_from_raw_file,
)
from core_silver.observation_converter.converters.exceptions import (
    FileExtractionError,
    FileSchemaError,
)
from core_silver.observation_converter.converters.nintendo_utils import (
    assign_nintendo_store_information,
)
from core_silver.observation_converter.pandas_utils import InvalidSchema, enforce_schema
from core_silver.utils.string_format import translate_column, unescape_html
from data_sdk.domain import DisplayPortal, Portal, countries, regions
from data_sdk.domain.platform import get_display_platform_from_platform


def _transform(parsed_df: pd.DataFrame) -> pd.DataFrame:
    if parsed_df.empty:
        return pd.DataFrame()

    """
    Nintendo in their reports has complicated format of data.
    Wishlist actions are kept in matrix with days, f.e.:

    |DATA----|07/14/21|07/15/21|07/16/21|07/17/21|07/18/21|07/19/21|07/20/21|07/21/21|07/22/21|
    |--------|--------|--------|--------|--------|--------|--------|--------|--------|--------|
    |raw-row-|1       |0       |0       |1       |1       |0       |0       |0       |0       |

    Purpose of this is to extract wishlist actions per day, and duplicate rows in such a way to have
    row with data for particular wishlist actions.
    For given example the output should by like this:

    |DATA----|Day-----|WishlistAdd---|
    |--------|--------|--------|
    |raw-row-|07/14/21|1       |
    |raw-row-|07/17/21|1       |
    |raw-row-|07/18/21|1       |

    We do not care about days without wishlist actions, our output should contain only
    row with unwishlist actions higher than 0
    """

    # Columns 11-.. contain dates that we want to unpivot
    # ex. 07/14/21 | 07/15/21 | 07/16/21 | 07/17/21
    unpivot_column_numbers = 10
    day_columns = list(parsed_df.columns[unpivot_column_numbers:])

    # Columns 0-10 contain information that we don't want to unpivot
    # ex. TitleCode | TitleName | ItemCode | ItemName | Region
    other_columns = list(parsed_df.columns[:unpivot_column_numbers])

    # Assign Day and Additions to the all report rows
    parsed_df = pd.melt(
        parsed_df,
        id_vars=other_columns,
        value_vars=day_columns,
        var_name="Day",
        value_name="Additions",
    )
    parsed_df = parsed_df.loc[parsed_df["Additions"] > 0]
    if parsed_df.empty:
        return pd.DataFrame()

    parsed_df["Day"] = parsed_df.apply(_parse_date, axis=1)
    parsed_df["Additions"] = parsed_df["Additions"].astype(int)

    return parsed_df


def _parse_date(row):
    if str(row["Day"]).find("/") in [1, 2]:
        return datetime.strptime(row["Day"], "%m/%d/%y")
    else:
        return datetime.strptime(row["Day"], "%y/%m/%d")


class NintendoWishlistActionsConverter(BaseWishlistActionsConverter):
    _schema = pa.DataFrameSchema(
        columns={
            "NsUid": Column(pa.String, coerce=True),
            "Code": Column(pa.String, coerce=True),
            "Name": Column(pa.String, coerce=True),
            "Region": Column(pa.String, coerce=True),
            "Platform": Column(pa.String, coerce=True),
            "Publisher": Column(pa.String, coerce=True),
            "Sales Total": Column(pa.Float, coerce=True),
            "Sales Conversion Rate": Column(pa.String, coerce=True),
            "Total": Column(pa.Int, coerce=True),
            "Period Total": Column(pa.Int, coerce=True),
        }
    )

    def parse(self, raw_file: bytes) -> pd.DataFrame:
        df = self._extract_file(raw_file)
        df = _transform(df)
        df.sort_index(ascending=True, inplace=True)
        try:
            return enforce_schema(df, self._schema)
        except InvalidSchema as ex:
            raise FileSchemaError(str(ex))

    def _convert(self) -> pd.DataFrame:
        parsed_df = self.parse(self._raw_report.raw_file)
        manifest = self._raw_report.metadata

        if parsed_df.empty:
            return pd.DataFrame()

        converted_df = pd.DataFrame()
        converted_df = converted_df.assign(
            date=parsed_df["Day"],
            purchases_and_activations=0,
            gifts=0,
            adds=parsed_df["Additions"],
            deletes=0,
            store_id=parsed_df["NsUid"],
            sku_id=parsed_df["Code"],
            human_name=translate_column(parsed_df["Name"], unescape_html),
            report_id=manifest.report_id,
            studio_id=manifest.studio_id,
            platform=parsed_df["Platform"].map(get_display_platform_from_platform),
            portal=DisplayPortal.NINTENDO,
            region=parsed_df["Region"].map(
                regions.get_nintendo_region_by_wishlist_actions_region
            ),
            country_code=countries.get_country_alpha_3("unknown"),
        )

        converted_df["unique_sku_id"] = (
            converted_df["sku_id"]
            + "-"
            + converted_df["region"].map(str.strip).str.split().str[-1].str.lower()
            + "-"
            + Portal.NINTENDO.value
            + ":"
            + converted_df["studio_id"].astype(str)
        )

        converted_df["portal_platform_region"] = generate_portal_platform_region(
            converted_df["portal"], converted_df["platform"], converted_df["region"]
        )

        converted_df = assign_nintendo_store_information(converted_df)

        return converted_df

    def _extract_file(self, raw_file: bytes) -> pd.DataFrame:
        try:
            raw_df = extract_csvs_from_raw_file(
                raw_file,
                sep=",",
                header=0,
                quotechar='"',
                skipfooter=1,
                engine="python",
                keep_default_na=False,
                na_values=[""],
                dtype={
                    "NsUid": str,
                    "Code": str,
                    "Name": str,
                    "Region": str,
                    "Platform": str,
                    "Publisher": str,
                    "Sales Total": np.float64,
                    "Sales Conversion Rate": str,
                    "Total": np.int64,
                    "Period Total": np.int64,
                },
            )
        except (ValueError, BadZipFile) as ex:
            raise FileExtractionError(str(ex))
        return raw_df
