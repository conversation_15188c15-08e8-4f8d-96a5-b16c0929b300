import io
from datetime import datetime
from zipfile import ZipFile

import pandas as pd
import pandera as pa
import polars as pl
from dateutil import tz

from core_silver.observation_converter.common_columns import (
    generate_portal_platform_region,
)
from core_silver.observation_converter.converters.base_converter import (
    BaseDiscountsConverter,
)
from core_silver.observation_converter.converters.discount_sanitize_functions import (
    deduplicate,
    sanitize,
)
from core_silver.observation_converter.zip import extract_validated_dfs
from core_silver.utils.string_format import (
    datetime_to_string,
    filter_nonalphanumeric,
    str_datetime_to_utc_datetime,
)
from data_sdk.domain import DisplayPortal, Portal
from data_sdk.domain.discounts import DiscountType
from data_sdk.domain.domain_types import ReportMetadataWithRawFile
from data_sdk.domain.flags import Flag
from data_sdk.domain.platform import DisplayPlatform
from data_sdk.domain.regions import Region, get_nintendo_region_by_country_code
from data_sdk.domain.tables import ExternalFeatureFlagsTable


class NintendoDiscountsConverter(BaseDiscountsConverter):
    _raw_csvs_schemas = {
        "discounts.csv": pa.DataFrameSchema(
            columns={
                "submission_id": pa.Column(pa.Int, coerce=True),
                "sale_name": pa.Column(pa.String, coerce=True),
                "discount_group_id": pa.Column(pa.Int, coerce=True),
                "product_code": pa.Column(pa.String, coerce=True),
                "ns_uid": pa.Column(pa.Int, coerce=True),
                "discount_id": pa.Column(pa.Int, coerce=True),
                "country_code": pa.Column(pa.String, coerce=True),
                "start_datetime": pa.Column(pa.String, coerce=True),
                "end_datetime": pa.Column(pa.String, coerce=True),
                "currency": pa.Column(pa.String, coerce=True),
                "discount_value": pa.Column(pa.Float, coerce=True),
                "regular_price": pa.Column(pa.Float, coerce=True),
                "price_start_datetime": pa.Column(pa.String, coerce=True),
                "price_end_datetime": pa.Column(pa.String, coerce=True, nullable=True),
                "platform_name": pa.Column(pa.String, coerce=True, nullable=True),
            },
            strict=True,
        ),
    }

    top_countries = {
        Region.NINTENDO_AMERICA.value: "US",
        Region.NINTENDO_ASIA.value: "KR",
        Region.NINTENDO_CHINA.value: "CN",
        Region.NINTENDO_EUROPE.value: "GB",
        Region.NINTENDO_JAPAN.value: "JP",
    }

    def __init__(
        self,
        raw_report: ReportMetadataWithRawFile,
        external_feature_flags_table: ExternalFeatureFlagsTable,
    ):
        self._external_feature_flags_table = external_feature_flags_table
        super().__init__(raw_report)

    def _convert(self) -> pd.DataFrame:
        parsed_df = self._parse(self._raw_report.raw_file)["discounts.csv"]

        if parsed_df.empty:
            return pd.DataFrame()

        df = pd.DataFrame()
        df = df.assign(
            region=parsed_df["country_code"].map(get_nintendo_region_by_country_code),
            country_code=parsed_df["country_code"],
            portal=DisplayPortal.NINTENDO,
            platform=DisplayPlatform.SWITCH,
            report_id=self._raw_report.metadata.report_id,
            create_time=self._raw_report.metadata.upload_date,
            update_time=self._raw_report.metadata.upload_date,
            studio_id=self._raw_report.metadata.studio_id,
            event_name=parsed_df["sale_name"],
            datetime_from=parsed_df["start_datetime"],
            datetime_to=parsed_df["end_datetime"],
            discount_depth=self._calculate_discount_depth(parsed_df),
            base_event_id=parsed_df["discount_group_id"],
            group_id=parsed_df["discount_group_id"],
            base_sku_id=parsed_df["product_code"].map(filter_nonalphanumeric),
            source_specific_discount_sku_id=parsed_df["ns_uid"],
            triggers_cooldown=True,
            major=False,
            discount_type=DiscountType.CUSTOM.value,
            price_increase_time=self._find_latest_price_change(parsed_df),
        )
        df["sales_unique_sku_id"] = df["base_sku_id"]
        df["unique_sku_id"] = self._generate_unique_sku_id(df)

        df[["datetime_from", "datetime_to", "discount_depth"]] = (
            self._aggregate_countries_to_regions(df)
        )

        df["unique_event_id"] = self._generate_unique_event_id(df)

        df["max_discount_percentage"] = df.groupby("unique_sku_id")[
            "discount_depth"
        ].transform("max")

        df["price_increase_time"] = df.groupby("unique_sku_id")[
            "price_increase_time"
        ].transform("max")

        df["is_event_joined"] = True
        df["portal_platform_region"] = generate_portal_platform_region(
            df["portal"], df["platform"], df["region"]
        )
        df["promo_length"] = (
            pd.to_datetime(df["datetime_to"]) - pd.to_datetime(df["datetime_from"])
        ).dt.total_seconds()

        date_time_columns = [
            "create_time",
            "update_time",
            "datetime_from",
            "datetime_to",
            "price_increase_time",
        ]
        df[date_time_columns] = df[date_time_columns].applymap(
            lambda x: pd.to_datetime(x, utc=True)
        )

        df = sanitize(pl.DataFrame(df))

        if self._external_feature_flags_table.has_flag(
            name=Flag.DEDUPLICATE_NINTENDO_DISCOUNTS
        ):
            df = deduplicate(df)

        df = df.sort(by=["datetime_from", "datetime_to", "unique_sku_id"]).to_pandas()

        return (
            df[self.converted_report_cls.model.to_schema().columns.keys()]
            .drop_duplicates()
            .reset_index(drop=True)
        )

    def _parse(self, raw_file: bytes) -> dict[str, pd.DataFrame]:
        with io.BytesIO(raw_file) as stream:
            return {
                **extract_validated_dfs(
                    zip_file=ZipFile(stream),
                    required_csv_files=("discounts.csv",),
                    get_schema=lambda filename: self._raw_csvs_schemas[filename],
                ),
            }

    def _calculate_discount_depth(self, df: pd.DataFrame) -> pd.Series:
        return (
            round(
                1
                - ((df["regular_price"] - df["discount_value"]) / df["regular_price"]),
                2,
            )
            * 100
        )

    def _generate_unique_sku_id(self, df: pd.DataFrame) -> pd.Series:
        return (
            df["base_sku_id"]
            + "-"
            + df["region"].map(str.strip).str.split().str[-1].str.lower()
            + "-"
            + Portal.NINTENDO.value
            + ":"
            + df["studio_id"].astype(str)
        )

    def _generate_unique_event_id(self, df: pd.DataFrame) -> pd.Series:
        return (
            df["datetime_from"].apply(datetime_to_string)
            + ":"
            + df["event_name"].map(filter_nonalphanumeric)
            + ":"
            + df["unique_sku_id"]
        )

    def _find_latest_price_change(self, df: pd.DataFrame):
        price_change_time = df.groupby(by=["product_code", "currency"]).apply(
            lambda x: str_datetime_to_utc_datetime(x["price_start_datetime"].max())
            if x["price_end_datetime"].notnull().any()
            else datetime.fromtimestamp(0, tz.tzutc())
        )
        return df.apply(
            lambda row: price_change_time[(row["product_code"], row["currency"])],
            axis=1,
        )

    def _aggregate_countries_to_regions(
        self, df: pd.DataFrame, top_countries: dict[str, str] = top_countries
    ) -> pd.DataFrame:
        output_df = df.copy()
        output_df[["datetime_from", "datetime_to"]] = output_df[
            ["datetime_from", "datetime_to"]
        ].applymap(str_datetime_to_utc_datetime)

        regional_event_timerange_df = pd.DataFrame()

        for region, country in top_countries.items():
            top_country = output_df[output_df["country_code"] == country]
            if top_country.empty:
                top_country = output_df[output_df["region"] == region].copy()
                top_country = top_country.assign(
                    datetime_from=top_country.groupby(["group_id", "unique_sku_id"])[
                        "datetime_from"
                    ].transform("min"),
                    datetime_to=top_country.groupby(["group_id", "unique_sku_id"])[
                        "datetime_to"
                    ].transform("max"),
                    discount_depth=top_country.groupby(["group_id", "unique_sku_id"])[
                        "discount_depth"
                    ].transform("min"),
                )
            if not top_country.empty:
                regional_event_timerange_df = pd.concat([
                    regional_event_timerange_df,
                    top_country,
                ])

        for idx, row in regional_event_timerange_df.iterrows():
            group_id, unique_sku_id, datetime_from, datetime_to, discount_depth = (
                row["group_id"],
                row["unique_sku_id"],
                row["datetime_from"],
                row["datetime_to"],
                row["discount_depth"],
            )
            output_df.loc[
                (output_df["group_id"] == group_id)
                & (output_df["unique_sku_id"] == unique_sku_id),
                ["datetime_from", "datetime_to", "discount_depth"],
            ] = [
                datetime_from,
                datetime_to,
                discount_depth,
            ]

        return output_df[["datetime_from", "datetime_to", "discount_depth"]]
