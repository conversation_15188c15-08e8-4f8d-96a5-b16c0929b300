import pandas as pd
import pandera as pa
from pandera import Column

from core_silver.dictionaries.constants import Constant
from core_silver.observation_converter.common_columns import (
    generate_portal_platform_region,
)
from core_silver.observation_converter.converters.base_converter import (
    BaseWishlistActionsConverter,
    extract_json_from_raw_zip_file,
)
from core_silver.observation_converter.converters.exceptions import FileSchemaError
from core_silver.observation_converter.converters.playstation_utils import (
    assign_playstation_store_information,
    normalize_dataframe,
)
from core_silver.observation_converter.pandas_utils import InvalidSchema, enforce_schema
from core_silver.utils.string_format import translate_column, unescape_html
from data_sdk.domain import DisplayPortal, Portal, regions
from data_sdk.domain.countries import get_country_alpha_3
from data_sdk.domain.platform import get_display_platform_from_platform
from data_sdk.domain.regions import _ps_region_dictionary


class PsWishlistActionsConverter(BaseWishlistActionsConverter):
    _schema = pa.DataFrameSchema(
        columns={
            "Date": Column(pa.DateTime, coerce=True),
            "Partner Name": Column(pa.String, coerce=True),
            "Concept ID": Column(pa.Int, coerce=True),
            "Concept": Column(pa.String, coerce=True),
            "Title ID": Column(pa.String, coerce=True),
            "Product ID": Column(pa.String, coerce=True),
            "Product Name": Column(pa.String, coerce=True),
            "Product Primary Class": Column(pa.String, coerce=True),
            "Product Secondary Class": Column(pa.String, coerce=True),
            "Product Tertiary Class": Column(pa.String, coerce=True),
            "Title Platform": Column(pa.String, coerce=True),
            "SIE Region": Column(pa.String, coerce=True),
            "Country Code": Column(pa.String, coerce=True),
            "Country/Region": Column(pa.String, coerce=True),
            "Wishlist Platform": Column(pa.String, coerce=True),
            "Additions": Column(pa.Int, coerce=True),
            "Deletions": Column(pa.Int, coerce=True),
            "Deletions by Purchase": Column(pa.Int, coerce=True),
            "Deletions by User": Column(pa.Int, coerce=True),
            "Deletions Other": Column(pa.Int, coerce=True),
        }
    )

    def parse(self, raw_file: bytes) -> pd.DataFrame:
        df = self._extract_file(raw_file)
        df = df.sort_index()  # todo: Check this. Is present in ps_analytics.py but looks like it's not needed here

        try:
            return enforce_schema(df, self._schema)
        except InvalidSchema as ex:
            raise FileSchemaError(str(ex))

    def _convert(self) -> pd.DataFrame:
        parsed_df = self.parse(self._raw_report.raw_file)
        manifest = self._raw_report.metadata

        if parsed_df.empty:
            return pd.DataFrame()
        converted_df = pd.DataFrame()
        converted_df = converted_df.assign(
            date=parsed_df["Date"].map(lambda x: x.strftime("%Y-%m-%d")),
            purchases_and_activations=parsed_df["Deletions by Purchase"],
            gifts=0,
            adds=parsed_df["Additions"],
            deletes=parsed_df["Deletions by User"] + parsed_df["Deletions Other"],
            store_id=parsed_df["Title ID"],
            sku_id=parsed_df["Product ID"],
            human_name=translate_column(parsed_df["Product Name"], unescape_html),
            report_id=manifest.report_id,
            studio_id=manifest.studio_id,
            platform=parsed_df["Title Platform"].map(
                lambda x: get_display_platform_from_platform(
                    x.lower()
                    if x.lower() != "not applicable"
                    else Constant.UNKNOWN.value
                )
            ),
            portal=DisplayPortal.PLAYSTATION,
            region=parsed_df["SIE Region"].where(
                parsed_df["SIE Region"].isin(_ps_region_dictionary.keys()),
                other=Constant.UNKNOWN.value,
            ),
            country_code=parsed_df["Country Code"].map(get_country_alpha_3),
        )

        # Detect Concept Products
        converted_df.loc[
            (converted_df["human_name"] == "Not applicable")
            & (converted_df["sku_id"] == "Not applicable")
        ] = converted_df.loc[
            (converted_df["human_name"] == "Not applicable")
            & (converted_df["sku_id"] == "Not applicable")
        ].assign(
            sku_id=parsed_df["Concept ID"].astype(str),
            store_id=parsed_df["Concept ID"].astype(str),
            human_name=translate_column(parsed_df["Concept"], unescape_html),
        )

        converted_df["unique_sku_id"] = (
            converted_df["sku_id"].astype(str)
            + "-"
            + converted_df["region"].astype(str)
            + "-"
            + Portal.PLAYSTATION.value
            + "-store:"
            + converted_df["studio_id"].astype(str)
        )
        converted_df["region"] = converted_df["region"].map(
            regions.get_ps_region_by_country_alpha_3
        )
        converted_df = assign_playstation_store_information(converted_df)

        converted_df["portal_platform_region"] = generate_portal_platform_region(
            converted_df["portal"], converted_df["platform"], converted_df["region"]
        )
        return converted_df

    def _extract_file(self, raw_file: bytes) -> pd.DataFrame:
        df = extract_json_from_raw_zip_file(raw_file=raw_file, orient="index")
        return normalize_dataframe(df)
