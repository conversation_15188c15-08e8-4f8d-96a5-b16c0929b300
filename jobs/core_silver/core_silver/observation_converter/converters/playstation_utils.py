import pandas as pd

from data_sdk.domain.regions import Region
from data_sdk.domain.stores import Store


def assign_playstation_store_information(converted_df: pd.DataFrame):
    converted_df["store"] = Store.PLAYSTATION_UNKNOWN
    converted_df.loc[
        (converted_df["region"] == Region.PLAYSTATION_AMERICA),
        "store",
    ] = Store.PLAYSTATION_AMERICA
    converted_df.loc[
        (converted_df["region"] == Region.PLAYSTATION_ASIA),
        "store",
    ] = Store.PLAYSTATION_ASIA
    converted_df.loc[
        (converted_df["region"] == Region.PLAYSTATION_EUROPE),
        "store",
    ] = Store.PLAYSTATION_EUROPE
    converted_df.loc[
        (converted_df["region"] == Region.PLAYSTATION_JAPAN),
        "store",
    ] = Store.PLAYSTATION_JAPAN

    converted_df["abbreviated_name"] = converted_df["store"].apply(
        lambda s: Store(s).abbreviate()
    )
    return converted_df


def normalize_dataframe(extracted_df: pd.DataFrame) -> pd.DataFrame:
    """
    PS API report uses nested lists in JSON file:
    {
        "datasource": "ccd78ab0-8f57-458c-b0c9-edae60b7e1c1",
        "columns": [Date, Partner Name, Franchise, Concept ID, ...],         <- columns
        "metadata": [{'type': 'DATE', 'dataSourceId': 'ccd78ab0-8f... }],
        "rows": [[2022-03-06, SUPERHOT SP. Z O.O., SUPERHOT, 2...]],         <- raw data
        "numRows": 2654,
        "numColumns": 35,
        "fromcache": False
    }
    This function operates on a dataframe created in the extract step
    by the function extract_json_from_raw_file.
    It flattens the dataframe to match the PSAnalyticsConverter schema.

    Parameters:
    extracted_df (pd.DataFrame): The extracted dataframe to be normalized.

    Returns:
    pd.DataFrame: The normalized dataframe.
    """
    if extracted_df.empty:
        return extracted_df
    columns = extracted_df.loc["columns", 0]
    data = extracted_df.loc["rows", 0]
    return pd.DataFrame(data, columns=columns)
