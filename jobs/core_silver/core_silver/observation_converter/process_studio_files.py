import logging
from datetime import datetime

import polars as pl
from elasticapm import capture_span

from core_silver.observation_converter.converters.converters_factory import (
    get_converter_class_using_source,
)
from core_silver.observation_converter.customer_rules.filters import (
    apply_customer_filters,
    load_customer_rules,
)
from core_silver.observation_converter.deduplicators.deduplicators_factory import (
    get_deduplicator,
)
from core_silver.utils.string_format import (
    generate_expected_filename,
    get_report_id_from_filename,
)
from data_sdk.custom_partition.partitioner import get_table_partition
from data_sdk.custom_partition.reader import CustomPartitionReader
from data_sdk.custom_partition.writer import CustomPartitionsWriter
from data_sdk.dependency_injection import generate_init_kwargs
from data_sdk.domain import Portal
from data_sdk.domain.domain_types import (
    ReportMetadata,
    ReportMetadataWithRawFile,
    ReportsMetadata,
    ReportState,
    StudioId,
)
from data_sdk.domain.observations import ObservationType
from data_sdk.domain.tables import (
    ExternalReportsTable,
    ObservationCumulativeWishlistSalesTable,
    ObservationDailyActiveUsersTable,
    ObservationDiscountsTable,
    ObservationSalesTable,
    ObservationVisibilityTable,
    ObservationWishlistActionsTable,
    ObservationWishlistBalanceTable,
    ObservationWishlistCohortsTable,
    SilverReportsTable,
    TableDefinition,
)
from data_sdk.reports.reader import ConvertedReportsReader, RawReportsReader
from data_sdk.reports.writer import ConvertedReportsWriter
from data_sdk.segmentator import SingleSegmentator

log = logging.getLogger(__name__)


def process_studio_files(
    studio_id: StudioId,
    portal: Portal,
    observation_type: ObservationType,
    creation_datetime: datetime,
    raw_reader: RawReportsReader,
    converted_reader: ConvertedReportsReader,
    converted_writer: ConvertedReportsWriter,
    result_reader: CustomPartitionReader,
    result_writer: CustomPartitionsWriter,
) -> None:
    log.info("Processing studio files")
    reports = result_reader.read_table(
        table_cls=ExternalReportsTable, studio_id=studio_id
    ).df.filter(
        (pl.col("state") != ReportState.DELETED.value)
        & (pl.col("portal") == portal.value)
        & (pl.col("observation_type") == observation_type.value)
    )
    raw_file_list = ReportsMetadata.model_validate(reports.to_dicts())
    if not raw_file_list.root:
        log.info(
            f"No raw files for studio_id: {studio_id}, portal: {portal} and observation_type: {observation_type} to process",
        )
        return

    missing_files = get_missing_files(
        raw_file_list, studio_id=studio_id, converted_reader=converted_reader
    )

    pending_files = get_pending_but_converted_files(
        raw_file_list=raw_file_list, missing_files=missing_files
    )
    if pending_files:
        for file in pending_files:
            log.info(
                "Report %s is converted but marked as PENDING, marking it as CONVERTED",
                file.report_id,
            )
            file.mark_converted()

    convert_missing_files(
        missing_files, raw_reader, converted_writer, result_reader, studio_id, portal
    )

    save_silver_reports(
        raw_file_list,
        result_writer,
        studio_id,
        portal,
        observation_type,
        creation_datetime,
    )

    converted_filename_list = get_converted_files(converted_reader, studio_id=studio_id)

    converted_filename_list = filter_by_report_ids(
        converted_filename_list, report_ids=reports["report_id"].to_list()
    )

    log.info("Deduplicating")
    with capture_span("deduplication"):
        deduplicator = get_deduplicator(
            portal=portal, observation_type=observation_type
        )()
        processed_observations = deduplicator.deduplicate(
            metadata_list=raw_file_list.root,
            converted_filename_list=converted_filename_list,
            converted_reader=converted_reader,
        )

    optional_customer_filters = load_customer_rules(studio_id=studio_id)

    if optional_customer_filters:
        log.info("Applying optional customer filters")
        processed_observations = apply_customer_filters(
            observations=processed_observations,
            customer_rules=optional_customer_filters,
        )

    table_cls = get_table(observation_type=observation_type)
    result_table = table_cls(df=processed_observations)

    log.info("Saving")
    partition = get_table_partition(result_table, studio_id=studio_id, portal=portal)

    segments = SingleSegmentator().create_segments(
        table=result_table, creation_datetime=creation_datetime
    )

    log.info("Saving partitioned table %s", partition)
    result_writer.save_table(result_table, partition, segments)
    log.info("finish")


def get_table(observation_type: ObservationType) -> type[TableDefinition]:
    obs_to_table_name: dict[ObservationType, type[TableDefinition]] = {
        ObservationType.SALES: ObservationSalesTable,
        ObservationType.DISCOUNTS: ObservationDiscountsTable,
        ObservationType.VISIBILITY: ObservationVisibilityTable,
        ObservationType.WISHLIST_ACTIONS: ObservationWishlistActionsTable,
        ObservationType.WISHLIST_COHORTS: ObservationWishlistCohortsTable,
        ObservationType.CUMULATIVE_WISHLIST_SALES: ObservationCumulativeWishlistSalesTable,
        ObservationType.WISHLIST_BALANCE: ObservationWishlistBalanceTable,
        ObservationType.DAILY_ACTIVE_USERS: ObservationDailyActiveUsersTable,
    }
    return obs_to_table_name[observation_type]


def get_converted_files(
    reader: ConvertedReportsReader, studio_id: StudioId
) -> list[str]:
    return reader.get_file_list(f"studio_id={studio_id}")


def filter_by_report_ids(
    converted_report_filenames: list[str], report_ids=list[int]
) -> list[str]:
    unique_report_ids = set(report_ids)
    return [
        report_filename
        for report_filename in converted_report_filenames
        if get_report_id_from_filename(report_filename) in unique_report_ids
    ]


def get_missing_files(
    raw_file_list: ReportsMetadata,
    studio_id: StudioId,
    converted_reader: ConvertedReportsReader,
) -> list[ReportMetadata]:
    already_converted_files = get_converted_files(converted_reader, studio_id=studio_id)
    # TODO ignore files with no_data flag
    return [
        file
        for file in raw_file_list.with_data.root
        if file.converted_filename not in already_converted_files
    ]


def get_pending_but_converted_files(
    raw_file_list: ReportsMetadata,
    missing_files: list[ReportMetadata],
) -> list[ReportMetadata]:
    missing_files_names: list[str] = [f.converted_filename for f in missing_files]
    return [
        file
        for file in raw_file_list.with_data.root
        if file.converted_filename not in missing_files_names
        and file.state == ReportState.PENDING
    ]


def convert_missing_files(
    missing_reports: list[ReportMetadata],
    raw_reader: RawReportsReader,
    converted_writer: ConvertedReportsWriter,
    result_reader: CustomPartitionReader,
    studio_id: StudioId,
    portal: Portal,
) -> None:
    """
    Convert missing files by checking which are not yet converted and converting them
    """
    log.info("convert_missing_files")

    if len(missing_reports) == 0:
        log.info("No reports to convert")
        return

    # We always convert reports with the same source, so we can use the first report to get the converter class
    converter_class = get_converter_class_using_source(missing_reports[0].source)

    log.info("Generating converter dependencies and reading external tables if needed")
    converter_kwargs = generate_init_kwargs(
        cls=converter_class,
        reader=result_reader,
        studio_id=studio_id,
        portal=portal,
    )

    for report in missing_reports:
        if report.no_data is True:
            log.info(f"Report {report.report_id} has no data, skipping conversion")
            report.mark_converted()
            continue

        file_with_metadata = ReportMetadataWithRawFile(
            raw_file=raw_reader.read(report.blob_name), metadata=report
        )

        try:
            converter = converter_class(file_with_metadata, **converter_kwargs)
            log.info(
                "Converting report %s from file %s", report.report_id, report.blob_name
            )
            converted_report = converter.convert()
        except Exception as e:
            log.exception(
                "Report id: %s, file '%s', conversion error %s",
                report.report_id,
                report.blob_name,
                str(e),  # noqa: TRY401
            )
            report.mark_failed()
            continue

        converted_writer.save_with_file_path(
            converted_report.df,
            f"studio_id={report.studio_id}/{generate_expected_filename(report)}",
        )
        report.mark_converted()


def save_silver_reports(
    raw_file_list: ReportsMetadata,
    result_writer: CustomPartitionsWriter,
    studio_id: StudioId,
    portal: Portal,
    observation_type: ObservationType,
    creation_datetime: datetime,
) -> None:
    result_table = SilverReportsTable(df=raw_file_list.to_lazy_frame().collect())
    partition = get_table_partition(
        result_table,
        studio_id=studio_id,
        portal=portal,
        observation_type=observation_type,
    )
    segments = SingleSegmentator().create_segments(
        table=result_table, creation_datetime=creation_datetime
    )

    result_writer.save_table(result_table, partition, segments)

    log.info("Silver reports saved")
