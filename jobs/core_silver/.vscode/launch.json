{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python: Current File",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal",
            "justMyCode": true
        },
        {
            "name": "Run locally",
            "type": "debugpy",
            "request": "launch",
            "program": "./scripts/run_locally.py",
            "console": "integratedTerminal",
            "justMyCode": true,
            "args": [
                "--input-env",
                "${input:input_env}",
                "--output-env",
                "${input:output_env}",
                "--studio-id",
                "${input:studio_id}",
                "--observation-type",
                "${input:observation_type}",
                "--portal",
                "${input:portal}"
            ]
        }
    ],
    "inputs": [
        {
            "id": "studio_id",
            "type": "promptString",
            "default": "1",
            "description": "Source studio_id"
        },
        {
            "id": "portal",
            "type": "pickString",
            "options": [
                "steam",
                "nintendo",
                "playstation",
                "microsoft",
                "humble",
                "gog",
                "meta",
                "epic",
                "apple",
                "google"
            ],
            "description": "Pick portal"
        },
        {
            "id": "observation_type",
            "type": "pickString",
            "options": [
                "sales",
                "wishlist_actions",
                "wishlist_cohorts",
                "visibility",
                "discounts"
            ],
            "description": "Pick observation_type"
        },
        {
            "id": "input_env",
            "type": "pickString",
            "options": ["local", "dev", "prod"],
            "default": "local",
            "description": "Silver env source"
        },
        {
            "id": "output_env",
            "type": "pickString",
            "options": ["local", "dev", "prod"],
            "default": "local",
            "description": "Silver env destination"
        }
    ]
}
