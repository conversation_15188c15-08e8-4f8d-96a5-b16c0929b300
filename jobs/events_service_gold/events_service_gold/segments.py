from datetime import datetime

import polars as pl
from data_sdk.domain.constants import PORTAL_ID
from data_sdk.domain.tables import TableDefinition
from data_sdk.segmentator import BaseSegmentator, SegmentDefinition
from data_sdk.utils.date_utils import datetime_to_string

PORTAL_ID_TO_NAME = {
    portal_id: portal_name.lower() for portal_name, portal_id in PORTAL_ID.items()
}


class PortalFieldSegmentator(BaseSegmentator):
    def create_segments(
        self, table: TableDefinition, creation_datetime: datetime
    ) -> list[SegmentDefinition]:
        chunks_folder_name = datetime_to_string(creation_datetime)
        custom_segments = []
        portals = [portal for portal in table.df.get_column("portal").unique()]
        for portal in portals:
            custom_segments.append(
                SegmentDefinition(
                    path=f"portal={str(portal).lower()}/{chunks_folder_name}",
                    mask_expression=(pl.col("portal") == portal),
                )
            )

        return custom_segments


class PortalPPRIDSegmentator(BaseSegmentator):
    def create_segments(
        self, table: TableDefinition, creation_datetime: datetime
    ) -> list[SegmentDefinition]:
        chunks_folder_name = datetime_to_string(creation_datetime)
        multiplier = 10000
        custom_segments = []
        portal_col = table.df.with_columns(
            (pl.col("portal_platform_region_id") // multiplier).alias("portal_id")
        )
        portal_ids = portal_col.get_column("portal_id").unique().to_list()
        for portal_id in portal_ids:
            portal_name = PORTAL_ID_TO_NAME.get(portal_id, "unknown")
            custom_segments.append(
                SegmentDefinition(
                    path=f"portal={portal_name}/{chunks_folder_name}",
                    mask_expression=(
                        pl.col("portal_platform_region_id") >= portal_id * multiplier
                    )
                    & (
                        pl.col("portal_platform_region_id")
                        < (portal_id + 1) * multiplier
                    ),
                )
            )

        return custom_segments
