from scripts.run_locally import main


def disable_test_aggregators_discount_no_scraped():
    main(input_env="dev", studio_id=10548)


def disable_test_aggregators_discount_empty():
    main(input_env="dev", studio_id=1434)


def disable_test_aggregators_missing_dim_sku():
    main(input_env="prod", studio_id=461)


def disable_test_aggregators():
    main(input_env="prod", studio_id=10940)


def disable_test_aggregators_dev():
    main(input_env="dev", studio_id=12)


def disable_test_aggregators_empty_fact_sales():
    main(input_env="prod", studio_id=10818)
