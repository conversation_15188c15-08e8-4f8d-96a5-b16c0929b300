import logging

import pandas as pd
import pandera as pa
import polars as pl
from data_sdk.aggregator import BaseAggregator
from data_sdk.domain.tables import (
    LegacyDimPortalsTable,
    LegacyDimSKUsTable,
    LegacyDimStudioTable,
    LegacyFactDetectedEventsTable,
    LegacyFactSalesTable,
    TableWithGoldPartitions,
)
from data_sdk.validators.schemas import NoCheckSchema

from ppt_gold.aggregators.discount_depth_input.discount_depth_input_logic import (
    _add_target_columns,
    _attach_retarded_features,
    _cap_discount_depths,
    _create_additional_features,
    _create_age_features,
    _create_daily_sales,
    _create_dlc_01_flag,
    _create_events_table,
    _create_retarded_daily_features,
    _cross_table_proliferation,
    _detect_Steam_events,
    _detect_US_holidays,
    _filter_portals_and_studios,
)
from ppt_gold.aggregators.tables import PPTGoldTable
from ppt_gold.utils.pandas_utils import validate_schema

log = logging.getLogger(__name__)


class FactDiscountDepthInputTable(TableWithGoldPartitions):
    table_name = PPTGoldTable.FACT_DISCOUNT_DEPTH_INPUT
    model = NoCheckSchema


class FactDiscountDepthInputAggregator(BaseAggregator):
    table_cls = FactDiscountDepthInputTable

    def __init__(
        self,
        dim_sku: LegacyDimSKUsTable,
        dim_studio: LegacyDimStudioTable,
        fact_sales: LegacyFactSalesTable,
        fact_detected_events: LegacyFactDetectedEventsTable,
        dim_portals: LegacyDimPortalsTable,
    ) -> None:
        self._dim_sku = dim_sku
        self._dim_studio = dim_studio
        self._dim_portals = dim_portals
        self._fact_sales = fact_sales
        self._fact_detected_events = fact_detected_events

    @property
    def schema(self) -> pa.DataFrameSchema:
        return pa.DataFrameSchema(
            columns={
                "unique_sku_id": pa.Column(pa.String, coerce=True),
                "studio_id": pa.Column(pa.Int, coerce=True),
                "portal_platform_region_id": pa.Column(pa.Int, coerce=True),
                "release_date": pa.Column(pa.String, coerce=True),
                "n_products": pa.Column(pa.Int, coerce=True),
                "regular_H1_norm_total": pa.Column(pa.Float, coerce=True),
                "prev_date_to": pa.Column(pa.String, coerce=True),
                "prev_date_from": pa.Column(pa.String, coerce=True),
                "small_game_01": pa.Column(pa.Int, coerce=True),
                "large_game_01": pa.Column(pa.Int, coerce=True),
                "prev_n_us_holidays": pa.Column(pa.Int, coerce=True),
                "prev_is_major_event": pa.Column(pa.Int, coerce=True),
                "prev_discount_pct": pa.Column(pa.Int, coerce=True),
                "prev_promo_length": pa.Column(pa.Int, coerce=True),
                "prev_is_steam_event": pa.Column(pa.Int, coerce=True),
                "prev_daily_sales_norm": pa.Column(pa.Float, coerce=True),
                "prev_total_sales_norm": pa.Column(pa.Float, coerce=True),
                "daily_benchmark": pa.Column(pa.Float, coerce=True),
                "all_depths": pa.Column(pa.String, coerce=True),
                "europe_01": pa.Column(pa.Int, coerce=True),
                "america_01": pa.Column(pa.Int, coerce=True),
                "am_x_prev_has_holiday": pa.Column(pa.Int, coerce=True),
                "n_dlcs": pa.Column(pa.Int, coerce=True),
                "annual_gso_norm_daily": pa.Column(pa.Float, coerce=True),
                "max_discount_sofar": pa.Column(pa.Int, coerce=True),
                "is_small_game": pa.Column(pa.Int, coerce=True),
                "is_large_game": pa.Column(pa.Int, coerce=True),
                "last_discount_date_to": pa.Column(pa.String, coerce=True),
                "last_discount_date_from": pa.Column(pa.String, coerce=True),
                "last_discount_us_holidays_count": pa.Column(pa.Int, coerce=True),
                "last_discount_is_major_event": pa.Column(pa.Int, coerce=True),
                "last_discount_discount_depth": pa.Column(pa.Int, coerce=True),
                "last_discount_promo_length": pa.Column(pa.Int, coerce=True),
                "last_discount_is_steam_event": pa.Column(pa.Int, coerce=True),
                "total_studio_products_count": pa.Column(pa.Int, coerce=True),
                "total_h1_regular_sales_normalized": pa.Column(pa.Float, coerce=True),
                "last_discount_daily_sales_normalized": pa.Column(
                    pa.Float, coerce=True
                ),
                "last_discount_total_sales_normalized": pa.Column(
                    pa.Float, coerce=True
                ),
            }
        )

    def aggregate_single_portal(self, portal: str) -> pl.DataFrame:
        dim_portals = self._dim_portals.df
        dim_portals = dim_portals.filter(pl.col("portal") == portal)
        all_pprs = set(dim_portals["portal_platform_region"].to_list())
        all_ppr_ids = set(dim_portals["portal_platform_region_id"].to_list())
        dim_portals = dim_portals.to_pandas()

        fact_sales = self._fact_sales.df
        if fact_sales.is_empty():
            return pl.DataFrame()

        fact_sales = fact_sales.filter(pl.col("portal_platform_region").is_in(all_pprs))
        if fact_sales.is_empty():
            return pl.DataFrame()
        fact_sales = fact_sales.to_pandas()

        fact_detected_events = self._fact_detected_events.df

        if fact_detected_events.is_empty():
            return pl.DataFrame()

        fact_detected_events = fact_detected_events.filter(
            pl.col("portal_platform_region_id").is_in(all_ppr_ids)
        )
        if fact_detected_events.is_empty():
            return pl.DataFrame()
        fact_detected_events = fact_detected_events.to_pandas()
        dim_sku = self._dim_sku.df
        dim_sku = dim_sku.filter(
            pl.col("portal_platform_region").is_in(all_pprs)
        ).to_pandas()

        events_calendar = pd.DataFrame()  # Only used for steam calculations
        if portal == "Steam":
            link_to_events = (
                "ppt_gold/aggregators/external_data/steam_events_history.csv"
            )

            events_calendar = pd.read_csv(link_to_events).sort_values(
                by="start_date", ascending=True, ignore_index=True
            )

        good_categories = {
            "Sale",
            "Free",
            "Return",
            "Free & Sale",
            "Non-billable: Epic",
            "Sale Adjustment",
            "Free & Return",
        }
        fact_sales = fact_sales[fact_sales["category"].isin(good_categories)]

        fact_detected_events = fact_detected_events[
            fact_detected_events.event_type.isin(("discount", "store_discount"))
            & fact_detected_events.promo_length.notna()
        ].reset_index()

        dim_sku["unique_sku_id"] = dim_sku["sku_studio"]
        dim_sku["human_name_clean"] = (
            dim_sku["human_name"].str.lower().str.replace(r"\W|_| ", "", regex=True)
        )
        base_sku_dlc = _create_dlc_01_flag(dim_sku)

        # Group by 'sku_studio' and get both the minimum and maximum 'date' where 'gross_sales' + 'gross_returned' > 0
        sku_rel_date = (
            (
                fact_sales[fact_sales["gross_sales"] + fact_sales["gross_returned"] > 0]
                .groupby("sku_studio")["date"]
                .agg(["min", "max"])
            )
            .reset_index()
            .rename(
                columns={
                    "sku_studio": "unique_sku_id",
                    "min": "release_date",
                    "max": "last_sales_date",
                }
            )
        )

        fact_detected_events["date"] = pd.Series(
            [
                pd.date_range(
                    start=r.date_from, end=r.date_to, freq="D", inclusive="both"
                )
                for r in fact_detected_events.itertuples()
            ]
        )
        events_daily = fact_detected_events.explode("date")

        daily_sales = _create_daily_sales(fact_sales)

        daily_sales, events_daily = _cross_table_proliferation(
            daily_sales, events_daily, dim_sku, base_sku_dlc
        )

        daily_sales, studio_stats = _create_retarded_daily_features(
            daily_sales, days_to_prediction=14
        )
        events_daily["event_id"] = (
            events_daily["unique_sku_id"] + ":" + events_daily["date_from"]
        )

        event_agg_full = _create_events_table(
            events_daily, filter_out_events=False, lower_total_usd_cap=1
        )
        if portal == "Steam":
            event_agg_full = _filter_portals_and_studios(event_agg_full)
            event_agg_full = _cap_discount_depths(event_agg_full, max_discount_depth=90)

        event_agg_full = _detect_Steam_events(event_agg_full, events_calendar)
        event_agg_full = _detect_US_holidays(event_agg_full)
        event_agg_full = _create_age_features(event_agg_full, sku_rel_date)
        event_agg_full = _create_additional_features(event_agg_full)

        last_data_full = event_agg_full.groupby("unique_sku_id").agg(
            {"depth_group": [list]}
        )
        last_data_full.columns = ["all_depths"]
        last_data_full.reset_index(inplace=True)

        event_agg_full = _attach_retarded_features(
            event_agg_full, studio_stats, dim_sku, daily_sales, base_sku_dlc
        )
        event_agg_full = _add_target_columns(
            event_agg_full, norm_shift=0
        )  # it's important that the variable 'norm_shift' is set to 0 for this use case

        event_agg_full = pd.merge(
            event_agg_full,
            dim_portals[["portal_platform_region_id", "region"]],
            on="portal_platform_region_id",
            how="left",
        )

        event_agg_full["region"] = event_agg_full["region"].apply(
            lambda x: x.split()[-1].lower()
        )

        # too few observations for Japan => combining it with Asia
        event_agg_full.loc[event_agg_full["region"] == "japan", "region"] = "asia"
        # creating binary features for two of the three remaining regions:
        event_agg_full["america_01"] = 1 * (event_agg_full["region"] == "america")
        event_agg_full["europe_01"] = 1 * (event_agg_full["region"] == "europe")

        event_agg_full["am_x_prev_has_holiday"] = (
            event_agg_full["america_01"] * event_agg_full["has_holiday"]
        )

        cols_to_take = [
            "unique_sku_id",
            "release_date",
            "portal_platform_region_id",
            "studio_id",
            "date_from",
            "date_to",
            "discount_pct",
            "has_holiday",
            "is_major_event",
            "is_steam_event",
            "n_us_holidays",
            "promo_length",
            "daily_sales",
            "daily_benchmark",
            "total_sales",
            "total_benchmark",
            "america_01",
            "daily_sales_norm",
            "total_sales_norm",
            "am_x_prev_has_holiday",
            "annual_gso_norm_daily",
            "europe_01",
            "max_discount_sofar",
        ]
        cols_to_rename = [
            "date_from",
            "date_to",
            "discount_pct",
            "has_holiday",
            "is_major_event",
            "is_steam_event",
            "n_us_holidays",
            "promo_length",
            "daily_sales_norm",
            "total_sales_norm",
            "daily_sales",
            "total_sales",
        ]
        names_dict = dict(zip(cols_to_rename, ["prev_" + c for c in cols_to_rename]))

        # the current last event is the previous event from the point of view of the event for which we need to advise on the discount depth
        event_agg_full.sort_values(
            by=["unique_sku_id", "date_from"], ascending=True, inplace=True
        )
        # this is the (semi-)persistent version of the prediction table, i.e. it will eventually be put in the reportprocessor and calculated on a daily bases
        # then it will be retrieved for the purpose of the discount planner and addtional columns will be added based on the user input
        prediction_table = (
            event_agg_full.drop_duplicates(
                subset=["unique_sku_id"], keep="last", ignore_index=True
            )[cols_to_take]
            .rename(columns=names_dict)
            .copy()
        )
        # attaching the features calculated on a studio level (keeping only the last, i.e., most recent, entry for each studio)
        prediction_table = pd.merge(
            prediction_table,
            studio_stats.sort_values(
                by=["studio_id", "date"], ascending=True, ignore_index=True
            ).drop_duplicates(subset=["studio_id"], keep="last")[
                ["studio_id", "n_products", "n_dlcs", "n_games"]
            ],
            on="studio_id",
            how="left",
        )

        # attaching features calculated on an SKU level (e.g. indicators for small or large game); again only the most recent entry for each SKU is kept
        prediction_table = pd.merge(
            prediction_table,
            daily_sales.sort_values(
                by=["unique_sku_id", "date"], ascending=True, ignore_index=True
            ).drop_duplicates(subset=["unique_sku_id"], keep="last")[
                [
                    "unique_sku_id",
                    "found_dlc",
                    "benchmark_regular_H1",
                    "small_game_01",
                    "large_game_01",
                ]
            ],
            on="unique_sku_id",
            how="left",
        )

        prediction_table["regular_H1_norm_total"] = (
            prediction_table["benchmark_regular_H1"]
            / prediction_table["total_benchmark"]
        )

        prediction_table = pd.merge(
            prediction_table, last_data_full, on=["unique_sku_id"], how="left"
        )

        prediction_table.regular_H1_norm_total = (
            prediction_table.regular_H1_norm_total.round(6)
        )
        prediction_table.prev_daily_sales_norm = (
            prediction_table.prev_daily_sales_norm.round(6)
        )
        prediction_table.prev_total_sales_norm = (
            prediction_table.prev_total_sales_norm.round(6)
        )
        prediction_table.daily_benchmark = prediction_table.daily_benchmark.round(6)
        prediction_table.annual_gso_norm_daily = (
            prediction_table.annual_gso_norm_daily.round(6)
        )

        result = prediction_table[
            [
                "unique_sku_id",
                "studio_id",
                "portal_platform_region_id",
                "release_date",
                "n_products",
                "regular_H1_norm_total",
                "prev_date_to",
                "prev_date_from",
                "small_game_01",
                "large_game_01",
                "prev_n_us_holidays",
                "prev_is_major_event",
                "prev_discount_pct",
                "prev_promo_length",
                "prev_is_steam_event",
                "prev_daily_sales_norm",
                "prev_total_sales_norm",
                "daily_benchmark",
                "all_depths",
                "europe_01",
                "america_01",
                "am_x_prev_has_holiday",
                "n_dlcs",
                "annual_gso_norm_daily",
                "max_discount_sofar",
            ]
        ]

        result = result.dropna()  # TODO: don't calculate stuff for SKU without discounts, possibly filter at least 100 usd in the past 60 days

        maerge_substitute = (result.copy())[
            [
                "unique_sku_id",
                "small_game_01",
                "large_game_01",
                "prev_date_to",
                "prev_date_from",
                "prev_n_us_holidays",
                "prev_is_major_event",
                "prev_discount_pct",
                "prev_promo_length",
                "prev_is_steam_event",
                "n_products",
                "regular_H1_norm_total",
                "prev_daily_sales_norm",
                "prev_total_sales_norm",
            ]
        ]

        # should be phased out in future release
        renamed_prediction_table = maerge_substitute.rename(
            columns={
                "small_game_01": "is_small_game",
                "large_game_01": "is_large_game",
                "prev_date_to": "last_discount_date_to",
                "prev_date_from": "last_discount_date_from",
                "prev_n_us_holidays": "last_discount_us_holidays_count",
                "prev_is_major_event": "last_discount_is_major_event",
                "prev_discount_pct": "last_discount_discount_depth",
                "prev_promo_length": "last_discount_promo_length",
                "prev_is_steam_event": "last_discount_is_steam_event",
                "n_products": "total_studio_products_count",
                "regular_H1_norm_total": "total_h1_regular_sales_normalized",
                "prev_daily_sales_norm": "last_discount_daily_sales_normalized",
                "prev_total_sales_norm": "last_discount_total_sales_normalized",
            }
        )
        result = pd.merge(
            result,
            renamed_prediction_table,
            on=[
                "unique_sku_id",
            ],
            how="outer",
        )
        return pl.DataFrame(validate_schema(result, self.schema)).select(
            self.schema.columns.keys()
        )

    def _aggregate(self) -> pl.DataFrame:
        dataframes_to_concat = []
        dim_portals = self._dim_portals.df
        if dim_portals.is_empty():
            return pl.DataFrame(
                pd.DataFrame(columns=self.schema.dtypes.keys()).astype(  # type: ignore
                    {col: str(dtype) for col, dtype in self.schema.dtypes.items()}
                )
            )
        all_portals = set(dim_portals["portal"].to_list())
        # convert enum to
        for portal in all_portals:
            portal_aggregation = self.aggregate_single_portal(portal)
            if portal_aggregation.is_empty():
                continue
            dataframes_to_concat.append(portal_aggregation)
        result = (
            pl.DataFrame(pl.concat(dataframes_to_concat))
            if dataframes_to_concat
            else pl.DataFrame()
        )
        if result.is_empty():
            return pl.DataFrame(
                pd.DataFrame(columns=self.schema.dtypes.keys()).astype(  # type: ignore
                    {col: str(dtype) for col, dtype in self.schema.dtypes.items()}
                )
            )
        return result
