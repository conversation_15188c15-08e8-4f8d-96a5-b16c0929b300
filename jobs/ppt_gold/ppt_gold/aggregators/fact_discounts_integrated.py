import logging

import numpy as np
import pandas as pd
import pandera as pa
import polars as pl
from data_sdk.aggregator import BaseAggregator
from data_sdk.domain.tables import (
    LegacyDimSKUsTable,
    LegacyFactDetectedEventsTable,
    LegacyFactDiscountsTable,
    LegacyFactSalesTable,
    TableWithGoldPartitions,
)
from data_sdk.utils.date_utils import datetime_to_string
from data_sdk.validators.schemas import NoCheckSchema
from events_calculator.api_v2.model.discount_event import DiscountEventType, EventOrigin

from ppt_gold.aggregators.tables import PPTGoldTable
from ppt_gold.utils.pandas_utils import get_release_date_and_last_date, validate_schema
from ppt_gold.utils.string_format import filter_nonalphanumeric
from ppt_gold.validators.columns import (
    Bool,
    EnumString,
    MediumString,
    NonNegativeFloat,
    NonNegativeInt,
    TinyString,
)

log = logging.getLogger(__name__)


class FactDiscountsIntegratedTable(TableWithGoldPartitions):
    table_name = PPTGoldTable.FACT_DISCOUNT_INTEGRATED
    model = NoCheckSchema


class FactDiscountsIntegratedAggregator(BaseAggregator):
    table_cls = FactDiscountsIntegratedTable

    def __init__(
        self,
        observation_discounts: LegacyFactDiscountsTable,
        fact_detected_discounts: LegacyFactDetectedEventsTable,
        fact_sales: LegacyFactSalesTable,
        dim_sku: LegacyDimSKUsTable,
    ) -> None:
        self._observation_discounts = observation_discounts
        self._fact_detected_discounts = fact_detected_discounts
        self._fact_sales = fact_sales
        self._dim_sku = dim_sku

    schema = pa.DataFrameSchema(
        columns={
            "unique_sku_id": MediumString(),
            "studio_id": NonNegativeInt(),
            "release_date": TinyString(
                nullable=True
            ),  # take from dected merge to scraped x
            "last_sales_date": TinyString(
                nullable=True
            ),  # take from dected merge to scraped x
            "discount_depth": NonNegativeFloat(nullable=True),
            "discount_type": TinyString(),
            "discount_event_type": TinyString(),  # detected --> store_discount and discount_event_type x/2
            "datetime_from": TinyString(),  # detected --> date_from x
            "datetime_to": TinyString(),  # detected --> date_to x
            "major": NonNegativeInt(),
            "base_sku_id": MediumString(),
            "human_name": MediumString(
                nullable=True
            ),  # take from dected merge to scraped x
            "product_name": MediumString(
                nullable=True
            ),  # take from dected merge to scraped
            "gso": NonNegativeFloat(
                nullable=True
            ),  # take from dected merge to scraped x
            # new columns
            "promo_length": NonNegativeFloat(
                nullable=True
            ),  # detected --> promo_length in days to seconds x
            "report_id": NonNegativeInt(),
            "create_time": TinyString(nullable=True),
            "update_time": TinyString(nullable=True),
            "is_event_joined": Bool(),  # detected --> True x
            "triggers_cooldown": Bool(),  # detected --> from ~major x
            "event_name": MediumString(),
            "unique_event_id": MediumString(),  # detected --> event_id / recalculate it x
            "max_discount_percentage": NonNegativeFloat(
                nullable=True
            ),  # TODO: In the future we want to move it to dim_sku
            "price_increase_time": TinyString(
                nullable=True
            ),  # TODO: In the future we want to move it to dim_sku
            "portal_platform_region_id": NonNegativeInt(),
            "event_origin": EnumString(EventOrigin),
        }
    )

    def _aggregate(self) -> pl.DataFrame:
        observation_discounts = self._observation_discounts.df.to_pandas()
        fact_detected_discounts = self._fact_detected_discounts.df.to_pandas()
        fact_sales = self._fact_sales.df.to_pandas()
        dim_sku = self._dim_sku.df.to_pandas().rename(
            columns={"sku_studio": "unique_sku_id"}
        )
        if (
            observation_discounts.empty & fact_detected_discounts.empty
        ) or fact_sales.empty:
            return pl.DataFrame(
                pd.DataFrame(columns=self.schema.dtypes.keys()).astype(  # type: ignore
                    {col: str(dtype) for col, dtype in self.schema.dtypes.items()}
                )
            )

        if observation_discounts.empty:
            filtered_fact_detected_discounts = (
                _convert_fact_detected_discounts_columns_to_observed_columns(
                    fact_detected_discounts
                )
            )
            return pl.DataFrame(
                validate_schema(filtered_fact_detected_discounts, self.schema)
            ).select(self.schema.columns.keys())

        elif fact_detected_discounts.empty:
            observation_discounts = (
                _convert_observation_discounts_columns_to_fact_columns(
                    observation_discounts,
                    dim_sku,
                    fact_sales,
                )
            )
            return pl.DataFrame(
                validate_schema(observation_discounts, self.schema)
            ).select(self.schema.columns.keys())

        filtered_fact_detected_discounts = fact_detected_discounts[
            ~fact_detected_discounts["unique_sku_id"].isin(
                observation_discounts["unique_sku_id"].unique()
            )
        ]
        if not filtered_fact_detected_discounts.empty:
            filtered_fact_detected_discounts = (
                _convert_fact_detected_discounts_columns_to_observed_columns(
                    filtered_fact_detected_discounts
                )
            )

        observation_discounts = _convert_observation_discounts_columns_to_fact_columns(
            observation_discounts,
            dim_sku,
            fact_sales,
        )
        final_df_discounts = pd.concat(
            [filtered_fact_detected_discounts, observation_discounts], ignore_index=True
        )
        final_df_discounts = final_df_discounts[self.schema.columns.keys()]
        return pl.DataFrame(validate_schema(final_df_discounts, self.schema)).select(
            self.schema.columns.keys()
        )


def _convert_fact_detected_discounts_columns_to_observed_columns(
    fact_detected_discounts: pd.DataFrame,
) -> pd.DataFrame:
    fact_detected_discounts["datetime_from"] = pd.to_datetime(
        fact_detected_discounts["date_from"]
    ).dt.tz_localize(None)
    fact_detected_discounts["datetime_to"] = pd.to_datetime(
        fact_detected_discounts["date_to"]
    ).dt.tz_localize(None)
    fact_detected_discounts["triggers_cooldown"] = np.where(
        fact_detected_discounts["major"] == 1, False, True
    )
    fact_detected_discounts = fact_detected_discounts[
        fact_detected_discounts["discount_type"].isin(["custom", "store"])
    ]
    fact_detected_discounts["discount_event_type"] = fact_detected_discounts[
        "event_type"
    ].apply(lambda x: "custom" if x == DiscountEventType.DISCOUNT.value else "store")
    fact_detected_discounts["event_origin"] = "detected"

    fact_detected_discounts["unique_event_id"] = (
        fact_detected_discounts["datetime_from"].apply(datetime_to_string)
        + ":"
        + fact_detected_discounts["event_name"].map(filter_nonalphanumeric)
        + ":"
        + fact_detected_discounts["unique_sku_id"]
    )
    fact_detected_discounts["is_event_joined"] = True
    fact_detected_discounts["promo_length"] = (
        fact_detected_discounts["datetime_to"]
        - fact_detected_discounts["datetime_from"]
    ).dt.total_seconds()
    fact_detected_discounts["report_id"] = 0
    fact_detected_discounts["create_time"] = pd.to_datetime("now").strftime(
        "%Y-%m-%d %H:%M:%S"
    )
    fact_detected_discounts["update_time"] = pd.to_datetime("now").strftime(
        "%Y-%m-%d %H:%M:%S"
    )
    fact_detected_discounts["price_increase_time"] = ""
    fact_detected_discounts["max_discount_percentage"] = (
        fact_detected_discounts.groupby(
            "unique_sku_id"
        )["discount_depth"].transform("max")
    )

    return fact_detected_discounts


def _convert_observation_discounts_columns_to_fact_columns(
    observation_discounts: pd.DataFrame,
    dim_sku: pd.DataFrame,
    fact_sales: pd.DataFrame,
) -> pd.DataFrame:
    release_date_and_last_date_df = get_release_date_and_last_date(fact_sales).drop(
        columns=["sku_studio"]
    )

    observation_discounts = pd.merge(
        observation_discounts,
        release_date_and_last_date_df,
        on="unique_sku_id",
        how="left",
    )
    observation_discounts = pd.merge(
        observation_discounts,
        dim_sku[["unique_sku_id", "human_name", "product_name", "gso"]],
        on="unique_sku_id",
        how="left",
    )
    observation_discounts["discount_event_type"] = observation_discounts[
        "discount_type"
    ]
    observation_discounts["event_origin"] = "scraped"
    return observation_discounts
