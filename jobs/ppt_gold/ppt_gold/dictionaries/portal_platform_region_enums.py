from data_sdk.domain.stores import Store


def store_to_snake_case(store_name: Store) -> str:
    """
    Convert a StoreName enum to a snake_case string.
    """
    return store_name.value.replace(" ", "_").lower()


ppri_to_store_name_dict = {
    171010: store_to_snake_case(Store.STEAM),
    151511: store_to_snake_case(Store.NINTENDO_SWITCH_AMERICA),
    151512: store_to_snake_case(Store.NINTENDO_SWITCH_ASIA),
    151513: store_to_snake_case(Store.NINTENDO_SWITCH_EUROPE),
    151514: store_to_snake_case(Store.NINTENDO_SWITCH_JAPAN),
}
