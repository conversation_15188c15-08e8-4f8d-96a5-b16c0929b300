from pydantic import AnyHttpUrl
from pydantic_settings import BaseSettings


class Config(BaseSettings):
    env: str = "local"
    app_version: str = "1.0.0"
    job_name: str = "find-shards-job"
    docker_tag: str = ""
    docker_build_timestamp: str = ""

    user_service_url: AnyHttpUrl = "https://user-service-v2.indiebi.com"
    user_service_api_key: str = "ibit-5vVax31EHqsbYwQ14NAlwqiIGlhhgiONv2ScrZPEmyqJQqBxjlEPrWRg5AEtxu61GGhP3fDYh9gLhBiC8F4FHN5Ovgv7CeVA8wU2VpCtWZwFmeg57OVH6jkFshh"

    dataset_manager_url: AnyHttpUrl = "https://dataset-manager.indiebi.com"
    dataset_manager_api_key: str = "ibit-cRL0Yv3VGHE6EajiK00p3g3oby2CKUHCebdMy4UN11svOhZHNuB3VtIfQVI9tQO4U7xhvAoqA0hvrrTyAUb3reShVWQSa1cwaXRrsORAc6vKvaqvQjxH5xpLAya"
