{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Python: Debug Tests",
      "type": "debugpy",
      "request": "launch",
      "program": "${file}",
      "purpose": ["debug-test"],
      "console": "integratedTerminal",
      "justMyCode": false
    },
    {
      "name": "Run locally",
      "type": "debugpy",
      "request": "launch",
      "program": "./scripts/run_locally.py",
      "console": "integratedTerminal",
      "justMyCode": true,
      "args": ["--studio-id", "${input:studio_id}"]
    }
  ],
  "inputs": [
    {
      "id": "studio_id",
      "type": "promptString",
      "default": "1",
      "description": "Source studio_id"
    }
  ]
}
