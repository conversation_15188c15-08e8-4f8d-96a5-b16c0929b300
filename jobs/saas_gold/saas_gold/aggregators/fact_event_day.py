import pendulum
import polars as pl

from data_sdk.aggregator import BaseAggregator
from data_sdk.domain.constants import get_portal_platform_region_id
from data_sdk.domain.tables import (
    LegacyDimSKUsTable,
    LegacyFactEventDayTable,
    LegacyFactSalesTable,
    ObservationDiscountsTable,
    TableWithGoldPartitions,
)
from data_sdk.validators.schemas import NoCheckSchema
from saas_gold.aggregators.tables import SaaSGoldTable
from saas_gold.segmentators.chained_mo_y_q_m import ChainedSegmentatorMoYQM


class FactEventDayTable(TableWithGoldPartitions):
    table_name = SaaSGoldTable.FACT_EVENT_DAY
    model = NoCheckSchema


class FactEventDayAggregator(BaseAggregator):
    table_cls = FactEventDayTable
    segmentator = ChainedSegmentatorMoYQM(date_column="date")

    def __init__(
        self,
        legacy_fact_event_day: LegacyFactEventDayTable,
        observation_discounts: ObservationDiscountsTable,
        legacy_fact_sales: LegacyFactSalesTable,
        legacy_dim_skus: LegacyDimSKUsTable,
    ) -> None:
        self._legacy_fact_event_day = legacy_fact_event_day
        self._observation_discounts = observation_discounts
        self._legacy_fact_sales = legacy_fact_sales
        self._legacy_dim_skus = legacy_dim_skus

    def _aggregate(self) -> pl.DataFrame:
        if self._legacy_fact_sales.df.is_empty():
            return pl.DataFrame()

        if not self._observation_discounts.df.is_empty():
            self._observation_discounts.df = self._observation_discounts.df.filter(
                pl.col("unique_sku_id").is_in(self._legacy_dim_skus.df["sku_studio"])
            )

        combined_raw_data = combine_raw_data(
            self._observation_discounts.df,
            self._legacy_fact_event_day.df,
        )

        return generate_fact_event_day(
            combined_raw_data,
            self._legacy_fact_sales.df,
            self._legacy_dim_skus.df,
        )


def combine_raw_data(
    observation_discounts: pl.DataFrame,
    raw_fact_event_day: pl.DataFrame,
) -> pl.DataFrame:
    if observation_discounts.is_empty():
        return raw_fact_event_day
    expanded_rows = []
    for row in observation_discounts.iter_rows(named=True):
        date_from = pendulum.instance(row["datetime_from"]).date()
        date_to = pendulum.instance(row["datetime_to"]).date()
        discount = round(row["discount_depth"] / 100, 2)
        studio_id = row["studio_id"]
        unique_sku_id = row["unique_sku_id"]
        event_name = row["event_name"]
        event_id = row["unique_event_id"]
        event_status = "finished" if date_to < pendulum.today().date() else "ongoing"
        dates_short = (
            date_from.format("MMM DD")
            + " "
            + (date_to.format("MMM DD") if event_status == "finished" else "ongoing")
        )
        promo_length = date_from.diff(date_to).in_days() + 1
        portal_platform_region = row["portal_platform_region"]
        for i in range(promo_length):
            date = date_from.add(days=i)
            event_day_number = (
                round(float(i / (promo_length - 1)), 2) if promo_length > 1 else 0.0
            )
            expanded_rows.append(
                [
                    date,
                    date_from,
                    date_to,
                    studio_id,
                    unique_sku_id,
                    event_name,
                    event_id,
                    event_status,
                    dates_short,
                    discount,
                    promo_length,
                    event_day_number,
                    portal_platform_region,
                ]
            )
    scraped_fact_event_day = pl.DataFrame(
        expanded_rows,
        schema=[
            "date",
            "date_from",
            "date_to",
            "studio_id",
            "unique_sku_id",
            "event_name",
            "event_id",
            "event_status",
            "dates_short",
            "discount",
            "promo_length",
            "event_day_number",
            "portal_platform_region",
        ],
    )

    scraped_fact_event_day = scraped_fact_event_day.with_columns(
        portal_platform_region_id=scraped_fact_event_day[
            "portal_platform_region"
        ].map_elements(get_portal_platform_region_id, return_dtype=pl.Int64)
    ).drop("portal_platform_region")

    scraped_fact_event_day = scraped_fact_event_day.filter(
        pl.col("date") < pendulum.tomorrow().date()
    )
    scraped_fact_event_day = scraped_fact_event_day.with_columns(
        type=pl.lit("Scraped"),
        event_description=pl.lit(None).cast(pl.String),
    )

    days_since_previous_df = (
        scraped_fact_event_day.group_by("unique_sku_id", "event_id")
        .agg(
            date_from=pl.col("date_from").first(),
            date_to=pl.col("date_to").first(),
        )
        .sort("unique_sku_id", "date_from")
    )
    days_since_previous_df = days_since_previous_df.with_columns(
        days_since_previous_discount=(
            pl.col("date_from") - pl.col("date_to").shift(1).over("unique_sku_id")
        )
        .dt.total_days()
        .fill_null(0),
    ).drop("date_from", "date_to")

    scraped_fact_event_day = scraped_fact_event_day.join(
        days_since_previous_df,
        on=["unique_sku_id", "event_id"],
        how="left",
        coalesce=True,
    ).select(raw_fact_event_day.columns)

    raw_fact_event_day = raw_fact_event_day.filter(
        ~pl.col("unique_sku_id").is_in(scraped_fact_event_day["unique_sku_id"].unique())
    )

    return pl.concat([raw_fact_event_day, scraped_fact_event_day]).sort(
        "unique_sku_id", "date"
    )


def generate_fact_event_day(
    raw_fact_event_day: pl.DataFrame,
    raw_fact_sales: pl.DataFrame,
    raw_dim_sku: pl.DataFrame,
) -> pl.DataFrame:
    if raw_fact_sales.is_empty():
        return pl.DataFrame()

    fact_event_day_lf = raw_fact_event_day.lazy()
    fact_sales = raw_fact_sales.lazy()
    dim_sku = raw_dim_sku.lazy()

    fact_sales = fact_sales.rename({"sku_studio": "unique_sku_id"})
    dim_sku = dim_sku.rename({"sku_studio": "unique_sku_id"})
    fact_event_day_lf = fact_event_day_lf.with_columns(
        pl.col("unique_sku_id").cast(pl.String).alias("unique_sku_id")
    )
    sku_avg_sales = (
        fact_event_day_lf.join(
            fact_sales.select(
                [
                    "date",
                    "unique_sku_id",
                    "gross_sales",
                ]
            ),
            on=["date", "unique_sku_id"],
            how="left",
            coalesce=True,
        )
        .group_by(
            [
                "unique_sku_id",
                "event_id",
            ]
        )
        .agg(
            avg_daily_sales=(pl.sum("gross_sales") / pl.max("promo_length")),
        )
    )

    fact_event_day_lf = fact_event_day_lf.join(
        sku_avg_sales,
        on=[
            "unique_sku_id",
            "event_id",
        ],
        how="left",
        coalesce=True,
    )

    fact_event_day_lf = fact_event_day_lf.join(
        dim_sku.select(
            [
                "unique_sku_id",
                "product_id",
                "product_name",
            ]
        ),
        on="unique_sku_id",
        how="left",
        coalesce=True,
    ).sort(
        [
            "date",
            "product_id",
            "portal_platform_region_id",
            "avg_daily_sales",
        ],
        descending=[False, False, False, True],
    )

    fact_event_day_lf = fact_event_day_lf.filter(
        (pl.col("product_name") != "__IGNORE") | (pl.col("product_name").is_null())
    )

    fact_event_day_lf = (
        fact_event_day_lf.group_by(["date", "product_id", "portal_platform_region_id"])
        .agg(
            pl.first("studio_id"),
            pl.first("discount"),
            pl.first("date_from").cast(pl.Date),
            pl.first("date_to").cast(pl.Date),
            pl.first("dates_short"),
            pl.first("promo_length"),
            pl.first("event_status"),
            pl.first("days_since_previous_discount"),
            pl.first("event_name"),
            pl.first("event_id"),
        )
        .sort("date")
    )

    fact_event_day_lf = fact_event_day_lf.with_columns(
        event_name_2=pl.when(pl.col("promo_length") == 1)
        .then(pl.col("date_from").cast(pl.String) + pl.lit(" (1 day)"))
        .otherwise(
            pl.col("date_from").cast(pl.String)
            + pl.lit(" (")
            + pl.col("promo_length").cast(pl.String)
            + pl.lit(" days)")
        ),
        year=pl.when(pl.col("date_from").dt.year() == pl.col("date_to").dt.year())
        .then(pl.col("date_from").dt.year().cast(pl.String))
        .otherwise(
            pl.col("date_from").dt.year().cast(pl.String)
            + pl.lit(" ")
            + pl.col("date_to").dt.year().cast(pl.String)
        ),
    )

    return fact_event_day_lf.with_columns(
        portal_platform_region_id=pl.col("portal_platform_region_id").cast(pl.Int64)
    ).collect()


def generate_fact_event_day_sku(
    raw_fact_event_day: pl.DataFrame,
    raw_fact_sales: pl.DataFrame,
    raw_dim_sku: pl.DataFrame,
) -> pl.DataFrame:
    if raw_fact_sales.is_empty():
        return pl.DataFrame()

    fact_event_day = raw_fact_event_day.lazy()
    fact_sales = raw_fact_sales.lazy()
    dim_sku = raw_dim_sku.lazy()

    fact_sales = fact_sales.rename({"sku_studio": "unique_sku_id"})
    dim_sku = dim_sku.rename({"sku_studio": "unique_sku_id"})
    fact_event_day = fact_event_day.with_columns(
        pl.col("unique_sku_id").cast(pl.String).alias("unique_sku_id")
    )
    sku_avg_sales = (
        fact_event_day.join(
            fact_sales.select(
                [
                    "date",
                    "unique_sku_id",
                    "gross_sales",
                ]
            ),
            on=["date", "unique_sku_id"],
            how="left",
            coalesce=True,
        )
        .group_by(
            [
                "unique_sku_id",
                "event_id",
            ]
        )
        .agg(
            avg_daily_sales=(pl.sum("gross_sales") / pl.max("promo_length")),
        )
    )

    fact_event_day = fact_event_day.join(
        sku_avg_sales,
        on=[
            "unique_sku_id",
            "event_id",
        ],
        how="left",
        coalesce=True,
    )

    fact_event_day = fact_event_day.join(
        dim_sku.select(
            [
                "unique_sku_id",
                "product_id",
            ]
        ),
        on="unique_sku_id",
        how="left",
        coalesce=True,
    ).sort(
        [
            "date",
            "product_id",
            "portal_platform_region_id",
            "avg_daily_sales",
        ],
        descending=[False, False, False, True],
    )
    fact_event_day = fact_event_day.with_columns(
        date_from=pl.col("date_from").cast(pl.Date),
        date_to=pl.col("date_to").cast(pl.Date),
    )
    fact_event_day = fact_event_day.with_columns(
        event_name_2=pl.when(pl.col("promo_length") == 1)
        .then(pl.col("date_from").cast(pl.String) + pl.lit(" (1 day)"))
        .otherwise(
            pl.col("date_from").cast(pl.String)
            + pl.lit(" (")
            + pl.col("promo_length").cast(pl.String)
            + pl.lit(" days)")
        ),
        year=pl.when(pl.col("date_from").dt.year() == pl.col("date_to").dt.year())
        .then(pl.col("date_from").dt.year().cast(pl.String))
        .otherwise(
            pl.col("date_from").dt.year().cast(pl.String)
            + pl.lit(" ")
            + pl.col("date_to").dt.year().cast(pl.String)
        ),
    )

    fact_event_day = fact_event_day.sort(
        "date", "unique_sku_id", "event_day_number"
    ).unique(["date", "unique_sku_id"], keep="first")

    return fact_event_day.with_columns(
        portal_platform_region_id=pl.col("portal_platform_region_id").cast(pl.Int64)
    ).collect()
