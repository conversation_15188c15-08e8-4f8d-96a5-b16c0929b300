import polars as pl

from data_sdk.aggregator import BaseAggregator
from data_sdk.domain import DisplayPortal
from data_sdk.domain.tables import (
    LegacyDimSKUsTable,
    LegacyFactEventDayTable,
    ObservationDiscountsTable,
    ObservationSalesTable,
    SilverSKUsTable,
    TableWithGoldPartitions,
)
from data_sdk.validators.schemas import NoCheckSchema
from saas_gold.aggregators.fact_event_day import combine_raw_data
from saas_gold.aggregators.schemas.output_table import FactBaselineSchema
from saas_gold.aggregators.tables import SaaSGoldTable
from saas_gold.segmentators.chained_mo_y_q_m import ChainedSegmentatorMoYQM
from saas_gold.utils.steam_fee_sale_multipliers import (
    apply_net_sales_multipliers_observation,
)


class FactBaselineTable(TableWithGoldPartitions):
    table_name = SaaSGoldTable.FACT_BASELINE
    model = NoCheckSchema


class FactBaselineAggregator(BaseAggregator):
    table_cls = FactBaselineTable
    segmentator = ChainedSegmentatorMoYQM(date_column="date")

    def __init__(
        self,
        legacy_dim_skus: LegacyDimSKUsTable,  # still needed because silver_skus table does not have product_id column
        legacy_fact_event_day: LegacyFactEventDayTable,
        observation_discounts: ObservationDiscountsTable,
        observation_sales: ObservationSalesTable,
        silver_skus: SilverSKUsTable,
    ) -> None:
        self._legacy_dim_skus = legacy_dim_skus
        self._legacy_fact_event_day = legacy_fact_event_day
        self._observation_discounts = observation_discounts
        self._observation_sales = observation_sales
        self._silver_skus = silver_skus

    def _aggregate(self) -> pl.DataFrame:
        if self._observation_sales.df.is_empty():
            return pl.DataFrame()
        combined_event_data = combine_raw_data(
            self._observation_discounts.df,
            self._legacy_fact_event_day.df,
        )
        if combined_event_data.is_empty():
            combined_event_data = pl.DataFrame(
                schema={"unique_sku_id": pl.String, "date": pl.Date}
            )
        promo_days = combined_event_data.with_columns(
            promo=pl.lit(True).cast(pl.Boolean),
        ).lazy()

        return generate_fact_baseline(
            self._observation_sales.df,
            promo_days,
            self._silver_skus.df.lazy(),
            self._legacy_dim_skus.df.lazy(),
        )


int_measures = [
    "free_units",
    "units_returned",
    "units_sold_directly",
    "units_sold_non_billable",
    "units_sold_retail",
]
float_measures = [
    "gross_returned",
    "gross_sales",
    "net_sales_approx",
]
baseline_int_measures = [f"baseline_{measure}" for measure in int_measures]
uplift_int_measures = [f"uplift_{measure}" for measure in int_measures]
baseline_float_measures = [f"baseline_{measure}" for measure in float_measures]
uplift_float_measures = [f"uplift_{measure}" for measure in float_measures]

measures = int_measures + float_measures
baseline_measures = baseline_int_measures + baseline_float_measures


def _prepare_fact_sales(
    observation_sales_lf: pl.LazyFrame,
) -> pl.LazyFrame:
    sold_directly_categories = pl.Series(
        [
            "Free & Sale",
            "Free & Return",
            "Sale",
            "Invalid Sale",
            "Return",
            "Invalid Return",
        ]
    )

    return observation_sales_lf.with_columns(
        [
            pl.when(pl.col("category").is_in(sold_directly_categories))
            .then(pl.col("units_sold"))
            .otherwise(pl.lit(0))
            .alias("units_sold_directly"),
            pl.when(pl.col("category").str.starts_with("Retail"))
            .then(pl.col("units_sold"))
            .otherwise(pl.lit(0))
            .alias("units_sold_retail"),
            pl.when(pl.col("category").str.starts_with("Non-billable"))
            .then(pl.col("units_sold"))
            .otherwise(pl.lit(0))
            .alias("units_sold_non_billable"),
            pl.when(pl.col("portal") == DisplayPortal.EPIC.value)
            .then(pl.col("net_sales") * 0.88)
            .otherwise(pl.col("net_sales") * 0.7)
            .alias("net_sales_approx"),
        ]
    )


def _filter_low_sales(
    fact_sales: pl.LazyFrame, coverage_threshold: float = 0.5
) -> tuple[pl.LazyFrame, pl.LazyFrame]:
    group_cols = ["unique_sku_id", "portal", "country_code"]

    filter_condition = (
        (
            (pl.col("gross_sales").sum() > 1_000)
            | ((pl.col("units_sold").sum() == 0) & (pl.col("free_units").sum() > 1_000))
        ).over(group_cols)
        &
        # Data coverage filter
        (
            pl.col("date").n_unique()
            >= ((pl.col("date").max() - pl.col("date").min()).dt.total_days() + 1)
            * coverage_threshold
        ).over(group_cols)
    )

    filtered_df = fact_sales.filter(filter_condition)
    removed_df = fact_sales.filter(~filter_condition)

    return filtered_df, removed_df


def _join_promo_days(
    observation_sales_lf: pl.LazyFrame,
    promo_days: pl.LazyFrame,
) -> pl.LazyFrame:
    return observation_sales_lf.join(
        promo_days.select(
            "unique_sku_id",
            "date",
            "promo",
        ),
        on=["unique_sku_id", "date"],
        how="left",
        coalesce=True,
    ).fill_null(False)


def _reset_promo_values(
    observation_sales_lf: pl.LazyFrame,
) -> pl.LazyFrame:
    return observation_sales_lf.with_columns(
        [
            pl.when(pl.col("promo")).then(None).otherwise(pl.col(col)).alias(col)
            for col in baseline_measures
        ]
    )


def _join_release_dates(
    observation_sales_lf: pl.LazyFrame,
    release_dates: pl.LazyFrame,
) -> pl.LazyFrame:
    return observation_sales_lf.join(
        release_dates,
        on="unique_sku_id",
        how="left",
        coalesce=True,
    )


def _restet_launch_dates(
    observation_sales_lf: pl.LazyFrame,
    duration: int = 30,
) -> pl.LazyFrame:
    return observation_sales_lf.with_columns(
        pl.when(pl.col("date") >= pl.col("release_date") + pl.duration(days=duration))
        .then(pl.col(col))
        .otherwise(None)
        for col in baseline_measures
    )


def _join_dim_skus(
    observation_sales_lf: pl.LazyFrame,
    dim_skus: pl.LazyFrame,
) -> pl.LazyFrame:
    return (
        observation_sales_lf.join(
            dim_skus.select(
                "sku_studio",
                "product_id",
                "product_name",
                "portal_platform_region_id",
            ),
            left_on="unique_sku_id",
            right_on="sku_studio",
            how="left",
            coalesce=True,
        )
        .filter(
            (pl.col("product_name") != "__IGNORE")
            & (pl.col("product_name").is_not_null())
        )
        .drop("product_name")
    )


def null_preserving_sum(column_name):
    return (
        pl.when(pl.col(column_name).is_not_null().any())
        .then(pl.col(column_name).sum())
        .otherwise(None)
        .alias(column_name)
    )


def group_by_and_aggregate(
    observation_sales_lf: pl.LazyFrame,
) -> pl.LazyFrame:
    return observation_sales_lf.group_by(
        ["date", "product_id", "country_code", "portal_platform_region_id"]
    ).agg(
        pl.first("studio_id"),
        null_preserving_sum("units_sold_directly"),
        null_preserving_sum("units_sold_non_billable"),
        null_preserving_sum("units_sold_retail"),
        null_preserving_sum("units_returned"),
        null_preserving_sum("gross_sales"),
        null_preserving_sum("gross_returned"),
        null_preserving_sum("free_units"),
        null_preserving_sum("net_sales_approx"),
        null_preserving_sum("baseline_units_sold_directly"),
        null_preserving_sum("baseline_units_sold_non_billable"),
        null_preserving_sum("baseline_units_sold_retail"),
        null_preserving_sum("baseline_units_returned"),
        null_preserving_sum("baseline_gross_sales"),
        null_preserving_sum("baseline_gross_returned"),
        null_preserving_sum("baseline_free_units"),
        null_preserving_sum("baseline_net_sales_approx"),
    )


def _calculate_uplift(baseline_lf: pl.LazyFrame) -> pl.LazyFrame:
    lf = baseline_lf
    for i in range(len(measures)):
        lf = lf.with_columns(
            (pl.col(measures[i]) - pl.col(f"baseline_{measures[i]}")).alias(
                f"uplift_{measures[i]}"
            )
        )
    return lf


def generate_fact_baseline(
    observation_sales: pl.DataFrame,
    promo_days: pl.LazyFrame,
    silver_skus: pl.LazyFrame,
    legacy_dim_skus: pl.LazyFrame,
) -> pl.DataFrame:
    observation_sales = observation_sales.with_columns(
        pl.col("unique_sku_id").cast(pl.String),
        pl.col("portal").cast(pl.String),
        pl.col("category").cast(pl.String),
    )
    observation_sales_lf = observation_sales.lazy().select(
        "date",
        "unique_sku_id",
        "portal",
        "studio_id",
        "units_returned",
        "units_sold",
        "free_units",
        "gross_returned",
        "gross_sales",
        "net_sales",
        "net_sales_approx",
        "country_code",
        "category",
    )
    observation_sales_lf = _prepare_fact_sales(
        observation_sales_lf,
    )

    observation_sales_lf = apply_net_sales_multipliers_observation(
        observation_sales_lf,
        pl.scan_parquet("saas_gold/dictionaries/steam_fee_skus_table.parquet"),
    )

    observation_sales_lf, removed_rows_lf = _filter_low_sales(
        observation_sales_lf,
        coverage_threshold=0.3,
    )

    observation_sales_lf = observation_sales_lf.with_columns(
        pl.col(col).alias(f"baseline_{col}") for col in measures
    )

    removed_rows_lf = removed_rows_lf.with_columns(
        pl.lit(0).alias(f"baseline_{col}") for col in measures
    )

    observation_sales_lf = _join_promo_days(
        observation_sales_lf,
        promo_days,
    )

    observation_sales_lf = _reset_promo_values(
        observation_sales_lf,
    )

    release_dates = silver_skus.select("unique_sku_id", "release_date")

    observation_sales_lf = _join_release_dates(
        observation_sales_lf,
        release_dates,
    )

    observation_sales_lf = _restet_launch_dates(
        observation_sales_lf,
    )

    observation_sales_lf = _join_dim_skus(
        observation_sales_lf,
        legacy_dim_skus,
    )

    removed_rows_lf = _join_dim_skus(
        removed_rows_lf,
        legacy_dim_skus,
    )

    fact_baseline_lf = group_by_and_aggregate(
        observation_sales_lf,
    )

    fact_baseline_removed_rows_lf = group_by_and_aggregate(
        removed_rows_lf,
    )

    original_columns = fact_baseline_lf.columns

    fact_baseline_lf = fact_baseline_lf.with_columns(
        pl.col("date").dt.weekday().alias("weekday")
    )

    group_keys = ["product_id", "country_code"]
    wide_keys = group_keys + ["weekday"]

    # Clip outliers for non-null values
    lower_q, upper_q = 0.05, 0.95
    for c in baseline_measures:
        # Calculate quantiles over groups, ignoring nulls
        q_low = pl.col(c).quantile(lower_q).over(wide_keys)
        q_high = pl.col(c).quantile(upper_q).over(wide_keys)

        fact_baseline_lf = fact_baseline_lf.with_columns(
            pl.when(pl.col(c).is_not_null())
            .then(
                pl.when(pl.col(c) < q_low)
                .then(q_low)
                .when(pl.col(c) > q_high)
                .then(q_high)
                .otherwise(pl.col(c))
            )
            .otherwise(None)
            .alias(c)
        )

    fact_baseline_lf = fact_baseline_lf.sort(wide_keys + ["date"])

    # Use interpolation for temporal filling within groups
    fact_baseline_lf = fact_baseline_lf.with_columns(
        pl.col(c).interpolate(method="linear").over(wide_keys).alias(f"{c}_interp")
        for c in baseline_measures
    )

    # Fill nulls with interpolated values
    fact_baseline_lf = fact_baseline_lf.with_columns(
        pl.coalesce(pl.col(c), pl.col(f"{c}_interp")).alias(c)
        for c in baseline_measures
    ).drop([f"{c}_interp" for c in baseline_measures])

    # Group medians with weekday
    fact_baseline_lf = fact_baseline_lf.with_columns(
        pl.col(c).fill_null(pl.col(c).median().over(wide_keys))
        for c in baseline_measures
    )

    # Forward/backward fill within groups
    fact_baseline_lf = fact_baseline_lf.with_columns(
        pl.col(c)
        .fill_null(strategy="forward")
        .fill_null(strategy="backward")
        .over(wide_keys)
        for c in baseline_measures
    )

    # Product-country medians (without weekday)
    fact_baseline_lf = fact_baseline_lf.with_columns(
        pl.col(c).fill_null(pl.col(c).median().over(group_keys))
        for c in baseline_measures
    )

    # Global medians
    fact_baseline_lf = fact_baseline_lf.with_columns(
        pl.col(c).fill_null(pl.col(c).median()) for c in baseline_measures
    )

    # Remove weekday and restore original column order
    fact_baseline_lf = fact_baseline_lf.drop("weekday").select(original_columns)

    fact_baseline_lf = fact_baseline_lf.with_columns(
        pl.col(baseline_measures).rolling_median(window_size=5).over(group_keys)
    )

    fact_baseline_lf = pl.concat(
        [
            fact_baseline_lf,
            fact_baseline_removed_rows_lf.with_columns(
                pl.lit(0).cast(pl.Float64).alias(col) for col in baseline_measures
            ),
        ]
    ).sort(["date", "product_id", "country_code"])

    fact_baseline_lf = _calculate_uplift(
        fact_baseline_lf,
    ).drop(measures)

    fact_baseline_lf = fact_baseline_lf.with_columns(
        pl.col(baseline_int_measures + uplift_int_measures).cast(pl.Int64)
    )

    FactBaselineSchema.validate(fact_baseline_lf)

    return fact_baseline_lf.collect()
