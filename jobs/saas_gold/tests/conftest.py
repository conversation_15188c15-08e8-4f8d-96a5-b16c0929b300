import datetime
import uuid
from datetime import date, timedelta

import pendulum
import polars as pl
import pytest
from factory.base import DictFactory
from factory.declarations import (
    Iterator,
    LazyAttribute,
    LazyAttributeSequence,
    LazyFunction,
    Sequence,
)

from data_sdk.domain.constants import get_portal_platform_region_id
from data_sdk.domain.observations import ObservationType
from data_sdk.domain.tables import (
    ExternalReportsTable,
    LegacyDimPortalsTable,
    LegacyDimSKUsTable,
    LegacyDimStudioTable,
    LegacyFactEventDayTable,
    LegacyFactSalesTable,
    LegacyFactVisibilityTable,
    LegacyFactWishlistActionsTable,
    LegacyFactWishlistCohortsTable,
    ObservationDiscountsTable,
    ObservationSalesTable,
    SilverReportsTable,
    SilverSKUsTable,
    SilverTrafficSourceTable,
)

STEAM_PC_GLOBAL = {
    "portal": "Steam",
    "platform": "PC",
    "region": "Global",
    "abbreviated_name": "Steam",
}


class LegacDimSKUFactory(DictFactory):
    class Params:
        input = STEAM_PC_GLOBAL

    base_sku_id = Sequence(lambda n: str(10000 + n))
    human_name = "SUPERHOT"
    store_id = "Unknown"
    studio_id = 1
    portal_platform_region = LazyAttribute(
        lambda o: f"{o.input['portal']}:{o.input['platform']}:{o.input['region']}"
    )
    human_name_indicator = ObservationType.SALES.value
    sku_type = "SALES"
    product_name = "Super Product"
    product_type = "GAME"
    sku_studio = LazyAttribute(
        lambda o: f"{o.base_sku_id}-{o.input['portal'].lower()}:{o.studio_id}"
    )
    portal_platform_region_id = LazyAttribute(
        lambda o: get_portal_platform_region_id(o.portal_platform_region)
    )
    product_id = LazyAttribute(
        lambda o: f"{o.product_name or 'Unassigned'}:{o.portal_platform_region_id}:{o.studio_id}"
    )
    package_name = None
    custom_group = None
    ratio = 1.0
    gso = 100
    is_baseline_precalculated = True

    @classmethod
    def build_table(cls, size: int = 1, **kwargs) -> LegacyDimSKUsTable:
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls._converted_rows_to_table(raw_rows)

    @classmethod
    def build_df_batch(cls, size, **kwargs) -> pl.DataFrame:
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls._converted_rows_to_table(raw_rows).df

    @staticmethod
    def _converted_rows_to_table(
        converted_rows: list[dict],
    ) -> LegacyDimSKUsTable:
        return LegacyDimSKUsTable(df=pl.DataFrame(converted_rows))


@pytest.fixture
def legacy_skus_factory() -> type[LegacDimSKUFactory]:
    LegacDimSKUFactory.reset_sequence(force=True)
    return LegacDimSKUFactory


class SilverSKUsFactory(DictFactory):
    class Params:
        input = STEAM_PC_GLOBAL

    unique_sku_id = LazyAttribute(
        lambda o: f"{o.base_sku_id}-{o.input['portal'].lower()}:{o.studio_id}"
    )
    base_sku_id = Sequence(lambda n: str(10000 + n))
    portal_platform_region = LazyAttribute(
        lambda o: f"{o.input['portal']}:{o.input['platform']}:{o.input['region']}"
    )
    human_name = "cool game"
    store_id = "Unknown"
    studio_id = 1
    human_name_indicator = ObservationType.SALES.value
    sku_type = "SALES"
    product_name = "Super Product"
    product_type = "GAME"
    release_date = LazyFunction(lambda: pendulum.date(2024, 6, 13))

    @classmethod
    def build_table(cls, size: int = 1, **kwargs) -> SilverSKUsTable:
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls._converted_rows_to_table(raw_rows)

    @classmethod
    def build_df_batch(cls, size, **kwargs) -> pl.DataFrame:
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls._converted_rows_to_table(raw_rows).df

    @staticmethod
    def _converted_rows_to_table(
        converted_rows: list[dict],
    ) -> SilverSKUsTable:
        return SilverSKUsTable(df=pl.DataFrame(converted_rows))


@pytest.fixture
def silver_skus_factory() -> type[SilverSKUsFactory]:
    SilverSKUsFactory.reset_sequence(force=True)
    return SilverSKUsFactory


class LegacyDimStudioFactory(DictFactory):
    studio_id = 13
    organization_id = "o-LSDrgad"
    email = "<EMAIL>"
    company_name = "Companieros"
    is_test_account = False
    is_verified = True
    agreement_date = "2019-11-12T12:58:39.923000Z"
    studio_parent_id = None

    @classmethod
    def build_table(cls, size: int = 1, **kwargs) -> LegacyDimStudioTable:
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls._converted_rows_to_table(raw_rows)

    @classmethod
    def build_df_batch(cls, size, **kwargs) -> pl.DataFrame:
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls._converted_rows_to_table(raw_rows).df

    @staticmethod
    def _converted_rows_to_table(
        converted_rows: list[dict],
    ) -> LegacyDimStudioTable:
        return LegacyDimStudioTable(df=pl.DataFrame(converted_rows))


@pytest.fixture
def legacy_dim_studio_factory() -> type[LegacyDimStudioFactory]:
    LegacyDimStudioFactory.reset_sequence(force=True)
    return LegacyDimStudioFactory


class SilverTrafficSourceFactory(DictFactory):
    page_category = "Home Page"
    page_category_group = "Home Page & Similar"
    page_feature = "Recommended by a Suggested Curator"
    hash_traffic_source = "5a8477c9735a03986af22d1wa964ac99"

    @classmethod
    def build_table(cls, size: int = 1, **kwargs) -> SilverTrafficSourceTable:
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls._converted_rows_to_table(raw_rows)

    @classmethod
    def build_df_batch(cls, size, **kwargs) -> pl.DataFrame:
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls._converted_rows_to_table(raw_rows).df

    @staticmethod
    def _converted_rows_to_table(
        converted_rows: list[dict],
    ) -> SilverTrafficSourceTable:
        return SilverTrafficSourceTable(df=pl.DataFrame(converted_rows))


@pytest.fixture
def silver_traffic_source_factory() -> type[SilverTrafficSourceFactory]:
    SilverTrafficSourceFactory.reset_sequence(force=True)
    return SilverTrafficSourceFactory


class LegacyDimPortalsFactory(DictFactory):
    portal = Iterator(["Steam", "PlayStation"])
    platform = Iterator(["PC", "PS4"])
    region = Iterator(["Global", "PS America"])
    store = Iterator(["Steam", "PlayStation America"])
    abbreviated_name = Iterator(["Steam", "PS US"])
    portal_platform_region = Iterator(["Steam:PC:Global", "PlayStation:PS4:PS America"])
    portal_platform_region_id = Iterator([171010, 162215])
    so_portal = Iterator([1, 4])
    so_platform = Iterator([0, 3])
    so_region = Iterator([0, 1])
    pso = Iterator([100, 431])

    @classmethod
    def build_table(cls, size: int = 1, **kwargs) -> LegacyDimPortalsTable:
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls._converted_rows_to_table(raw_rows)

    @classmethod
    def build_df_batch(cls, size, **kwargs) -> pl.DataFrame:
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls._converted_rows_to_table(raw_rows).df

    @staticmethod
    def _converted_rows_to_table(
        converted_rows: list[dict],
    ) -> LegacyDimPortalsTable:
        return LegacyDimPortalsTable(df=pl.DataFrame(converted_rows))


@pytest.fixture
def legacy_dim_portals_factory() -> type[LegacyDimPortalsFactory]:
    LegacyDimPortalsFactory.reset_sequence(force=True)
    return LegacyDimPortalsFactory


class LegacyFactSalesFactory(DictFactory):
    class Params:
        input = STEAM_PC_GLOBAL
        base_sku_id = 10000
        start_date = date(year=2000, month=1, day=1)

    country_code = "USA"
    currency_code = "USD"
    studio_id = 1
    sku_studio = LazyAttribute(
        lambda o: f"{o.base_sku_id}-{o.input['portal'].lower()}:{o.studio_id}"
    )
    bundle_name = "Direct Package Sale"
    portal_platform_region = LazyAttribute(
        lambda o: f"{o.input['portal']}:{o.input['platform']}:{o.input['region']}"
    )
    portal_platform_region_id = LazyAttribute(
        lambda o: get_portal_platform_region_id(o.portal_platform_region)
    )
    product_id = LazyAttribute(
        lambda o: f"Super Product:{o.portal_platform_region_id}:{o.studio_id}"
    )
    hash_acquisition_properties = "48eeba97a3e04ab96ae5fbfb87d1a8ad"
    date = LazyAttributeSequence(lambda o, n: o.start_date + timedelta(days=n))
    date_sku_studio = LazyAttribute(
        lambda o: f"{o.date.strftime('%Y-%m-%d')}:{o.sku_studio}"
    )
    source_file_id = 500123
    retailer_tag = "Direct Package Sale"
    base_price_local = 22.99
    calculated_base_price_usd = 24.99
    net_sales = 2200
    gross_returned = 10
    gross_sales = 2499
    units_returned = 1
    units_sold = 100
    free_units = 100
    price_local = 22.99
    price_usd = 24.99
    net_sales_approx = 2000
    category = "Sale"
    calculated_base_price_local_v2 = 22.99
    calculated_base_price_usd_v2 = 24.99

    @classmethod
    def build_table(cls, size: int = 1, **kwargs) -> LegacyFactSalesTable:
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls._converted_rows_to_table(raw_rows)

    @classmethod
    def build_df_batch(cls, size, **kwargs) -> pl.DataFrame:
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls._converted_rows_to_table(raw_rows).df

    @staticmethod
    def _converted_rows_to_table(
        converted_rows: list[dict],
    ) -> LegacyFactSalesTable:
        return LegacyFactSalesTable(df=pl.DataFrame(converted_rows))


@pytest.fixture
def legacy_fact_sales_factory() -> type[LegacyFactSalesFactory]:
    LegacyFactSalesFactory.reset_sequence(force=True)
    return LegacyFactSalesFactory


class ObservationSalesFactory(DictFactory):
    class Params:
        input = STEAM_PC_GLOBAL
        base_sku_id = 10000
        start_date = date(year=2000, month=1, day=1)

    sku_id = LazyAttribute(lambda o: str(o.base_sku_id))
    portal = LazyAttribute(lambda o: o.input["portal"])
    platform = LazyAttribute(lambda o: o.input["platform"])
    region = LazyAttribute(lambda o: o.input["region"])
    transaction_type = "Unknown"
    payment_instrument = "Unknown"
    tax_type = "Unknown"
    sale_modificator = "NOT_APPLICABLE"
    acquisition_platform = "Unknown"
    acquisition_origin = "MAIN_STORE"
    iap_flag = False
    date = LazyAttributeSequence(lambda o, n: o.start_date + timedelta(days=n))
    retailer_tag = "Direct Package Sale"
    human_name = "cool game"
    store_id = "Unknown"
    base_price_local = 22.99
    bundle_name = "NOT_APPLICABLE"
    net_sales = 2200
    gross_returned = 10
    gross_sales = 2499
    units_returned = 1
    units_sold = 100
    free_units = 100
    price_local = 22.99
    price_usd = 24.99
    store = "Steam"
    abbreviated_name = "Steam"
    net_sales_approx = 2000
    category = "Sale"
    studio_id = 1
    report_id = Sequence(lambda n: 8000 + n)
    unique_sku_id = LazyAttribute(
        lambda o: f"{o.base_sku_id}-{o.input['portal'].lower()}:{o.studio_id}"
    )
    country_code = "USA"
    currency_code = "USD"
    hash_acquisition_properties = "48eeba97a3e04ab96ae5fbfb87d1a8ad"
    portal_platform_region = LazyAttribute(
        lambda o: f"{o.input['portal']}:{o.input['platform']}:{o.input['region']}"
    )

    @classmethod
    def build_table(cls, size: int = 1, **kwargs) -> ObservationSalesTable:
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls._converted_rows_to_table(raw_rows)

    @classmethod
    def build_df_batch(cls, size, **kwargs) -> pl.DataFrame:
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls._converted_rows_to_table(raw_rows).df

    @staticmethod
    def _converted_rows_to_table(
        converted_rows: list[dict],
    ) -> ObservationSalesTable:
        return ObservationSalesTable(df=pl.DataFrame(converted_rows))


@pytest.fixture
def observation_sales_factory() -> type[ObservationSalesFactory]:
    ObservationSalesFactory.reset_sequence(force=True)
    return ObservationSalesFactory


class LegacyFactWishlistActionsFactory(DictFactory):
    class Params:
        input = STEAM_PC_GLOBAL
        base_sku_id = 10000
        start_date = date(year=2000, month=1, day=1)

    portal_platform_region: str = LazyAttribute(
        lambda o: f"{o.input['portal']}:{o.input['platform']}:{o.input['region']}"
    )
    portal_platform_region_id = LazyAttribute(
        lambda o: get_portal_platform_region_id(o.portal_platform_region)
    )
    product_id: str = LazyAttribute(
        lambda o: f"Super Product:{o.portal_platform_region_id}:{o.studio_id}"
    )
    sku_studio: str = LazyAttribute(lambda o: f"{o.base_sku_id}-store:{o.studio_id}")
    date = LazyAttributeSequence(lambda o, n: o.start_date + timedelta(days=n))
    studio_id = 1
    date_sku_studio = LazyAttribute(
        lambda o: f"{o.date.strftime('%Y-%m-%d')}:{o.sku_studio}"
    )
    date_product_studio: str = LazyAttribute(
        lambda o: f"{o.date.strftime('%Y-%m-%d')}:Super Product:{o.studio_id}"
    )
    adds = 10
    deletes = 5
    purchases_and_activations = 2
    gifts = 1
    country_code = "USA"

    @classmethod
    def build_table(cls, size: int = 1, **kwargs) -> LegacyFactWishlistActionsTable:
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls._converted_rows_to_table(raw_rows)

    @classmethod
    def build_df_batch(cls, size, **kwargs) -> pl.DataFrame:
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls._converted_rows_to_table(raw_rows).df

    @staticmethod
    def _converted_rows_to_table(
        converted_rows: list[dict],
    ) -> LegacyFactWishlistActionsTable:
        return LegacyFactWishlistActionsTable(df=pl.DataFrame(converted_rows))


@pytest.fixture
def legacy_fact_wishlist_actions_factory() -> type[LegacyFactWishlistActionsFactory]:
    LegacyFactWishlistActionsFactory.reset_sequence(force=True)
    return LegacyFactWishlistActionsFactory


class LegacyFactWishlistCohortsFactory(DictFactory):
    class Params:
        input = STEAM_PC_GLOBAL
        base_sku_id = 10000
        start_date = date(year=2000, month=1, day=1)

    portal_platform_region: str = LazyAttribute(
        lambda o: f"{o.input['portal']}:{o.input['platform']}:{o.input['region']}"
    )
    portal_platform_region_id = LazyAttribute(
        lambda o: get_portal_platform_region_id(o.portal_platform_region)
    )
    product_id: str = LazyAttribute(
        lambda o: f"Super Product:{o.portal_platform_region_id}:{o.studio_id}"
    )
    sku_studio: str = LazyAttribute(lambda o: f"{o.base_sku_id}-store:{o.studio_id}")
    date = LazyAttributeSequence(lambda o, n: o.start_date + timedelta(days=n))
    studio_id = 1
    date_sku_studio = LazyAttribute(
        lambda o: f"{o.date.strftime('%Y-%m-%d')}:{o.sku_studio}"
    )
    date_product_studio: str = LazyAttribute(
        lambda o: f"{o.date.strftime('%Y-%m-%d')}:Super Product:{o.studio_id}"
    )
    month_cohort = LazyAttribute(
        lambda o: datetime.date(o.start_date.year, o.start_date.month, 1)
    )
    total_conversions = 3
    purchases_and_activations = 2
    gifts = 1

    @classmethod
    def build_table(cls, size: int = 1, **kwargs) -> LegacyFactWishlistCohortsTable:
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls._converted_rows_to_table(raw_rows)

    @classmethod
    def build_df_batch(cls, size, **kwargs) -> pl.DataFrame:
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls._converted_rows_to_table(raw_rows).df

    @staticmethod
    def _converted_rows_to_table(
        converted_rows: list[dict],
    ) -> LegacyFactWishlistCohortsTable:
        return LegacyFactWishlistCohortsTable(df=pl.DataFrame(converted_rows))


@pytest.fixture
def legacy_fact_wishlist_cohorts_factory() -> type[LegacyFactWishlistCohortsFactory]:
    LegacyFactWishlistCohortsFactory.reset_sequence(force=True)
    return LegacyFactWishlistCohortsFactory


class LegacyFactVisibilityFactory(DictFactory):
    class Params:
        input = STEAM_PC_GLOBAL
        base_sku_id = 10000
        start_date = date(year=2000, month=1, day=1)

    portal_platform_region: str = LazyAttribute(
        lambda o: f"{o.input['portal']}:{o.input['platform']}:{o.input['region']}"
    )
    portal_platform_region_id = LazyAttribute(
        lambda o: get_portal_platform_region_id(o.portal_platform_region)
    )
    product_id: str = LazyAttribute(
        lambda o: f"Super Product:{o.portal_platform_region_id}:{o.studio_id}"
    )
    sku_studio: str = LazyAttribute(lambda o: f"{o.base_sku_id}-store:{o.studio_id}")
    date = LazyAttributeSequence(lambda o, n: o.start_date + timedelta(days=n))
    studio_id = 1
    date_sku_studio = LazyAttribute(
        lambda o: f"{o.date.strftime('%Y-%m-%d')}:{o.sku_studio}"
    )
    hash_traffic_source: str = "ffeea2741f0568cb042573c6ed22da96"
    date_product_studio: str = LazyAttribute(
        lambda o: f"{o.date.strftime('%Y-%m-%d')}:Super Product:{o.studio_id}"
    )
    visits = 10
    owner_visits = 1
    impressions = 100
    owner_impressions = 10
    navigation = "Direct Navigation"

    @classmethod
    def build_table(cls, size: int = 1, **kwargs) -> LegacyFactVisibilityTable:
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls._converted_rows_to_table(raw_rows)

    @classmethod
    def build_df_batch(cls, size, **kwargs) -> pl.DataFrame:
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls._converted_rows_to_table(raw_rows).df

    @staticmethod
    def _converted_rows_to_table(
        converted_rows: list[dict],
    ) -> LegacyFactVisibilityTable:
        return LegacyFactVisibilityTable(df=pl.DataFrame(converted_rows))


@pytest.fixture
def legacy_fact_visibility_factory() -> type[LegacyFactVisibilityFactory]:
    LegacyFactVisibilityFactory.reset_sequence(force=True)
    return LegacyFactVisibilityFactory


class ExternalReportsFactory(DictFactory):
    report_id = Sequence(lambda n: 8000 + n)
    studio_id = 13
    source = "steam_sales"
    portal = "steam"
    observation_type = "sales"
    date_from = LazyFunction(lambda: pendulum.datetime(2024, 6, 13, 5, 25, 28, 670000))
    date_to = LazyFunction(lambda: pendulum.datetime(2024, 6, 27, 5, 25, 28, 670000))
    upload_date = LazyFunction(
        lambda: pendulum.datetime(2024, 6, 28, 5, 25, 28, 670000)
    )
    blob_name = Sequence(lambda n: f"upload/{uuid.uuid4().hex[:32]}")
    original_name = "STEAM_SALES-2024-06-13_2024-06-28.zip"
    state = Iterator([s for s in ("CONVERTED", "PENDING")])
    no_data = Sequence(lambda n: n % 10 == 0)

    @classmethod
    def build_table(cls, size: int = 1, **kwargs) -> ExternalReportsTable:
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls._converted_rows_to_table(raw_rows)

    @classmethod
    def build_df_batch(cls, size, **kwargs) -> pl.DataFrame:
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls._converted_rows_to_table(raw_rows).df

    @staticmethod
    def _converted_rows_to_table(
        converted_rows: list[dict],
    ) -> ExternalReportsTable:
        return ExternalReportsTable(df=pl.DataFrame(converted_rows))


@pytest.fixture
def external_reports_factory() -> type[ExternalReportsFactory]:
    ExternalReportsFactory.reset_sequence(force=True)
    return ExternalReportsFactory


class SilverReportsFactory(DictFactory):
    report_id = Sequence(lambda n: 8000 + n)
    studio_id = 13
    source = "steam_sales"
    portal = "steam"
    observation_type = "sales"
    date_from = LazyFunction(lambda: pendulum.datetime(2024, 6, 13, 5, 25, 28, 670000))
    date_to = LazyFunction(lambda: pendulum.datetime(2024, 6, 28, 5, 25, 28, 670000))
    upload_date = LazyFunction(
        lambda: pendulum.datetime(2024, 6, 28, 5, 25, 28, 670000)
    )
    blob_name = Sequence(lambda n: f"upload/{uuid.uuid4().hex[:32]}")
    original_name = "STEAM_SALES-2024-06-13_2024-06-28.zip"
    state = "CONVERTED"
    no_data = Sequence(lambda n: n % 10 == 0)

    @classmethod
    def build_table(cls, size: int = 1, **kwargs) -> SilverReportsTable:
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls._converted_rows_to_table(raw_rows)

    @classmethod
    def build_df_batch(cls, size, **kwargs) -> pl.DataFrame:
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls._converted_rows_to_table(raw_rows).df

    @staticmethod
    def _converted_rows_to_table(
        converted_rows: list[dict],
    ) -> SilverReportsTable:
        return SilverReportsTable(df=pl.DataFrame(converted_rows))


@pytest.fixture
def silver_reports_factory() -> type[SilverReportsFactory]:
    SilverReportsFactory.reset_sequence(force=True)
    return SilverReportsFactory


class LegacyFactEventDayFactory(DictFactory):
    class Params:
        input = STEAM_PC_GLOBAL

    discount = 0.2
    date = LazyFunction(lambda: pendulum.date(2024, 6, 13))
    studio_id = 13
    unique_sku_id = LazyAttribute(
        lambda o: f"10000-{o.input['portal'].lower()}:{o.studio_id}"
    )
    portal_platform_region_id = LazyAttribute(
        lambda o: get_portal_platform_region_id(
            f"{o.input['portal']}:{o.input['platform']}:{o.input['region']}"
        )
    )
    date_from = LazyAttribute(lambda o: o.date - timedelta(days=1))
    date_to = LazyAttribute(lambda o: o.date + timedelta(days=1))
    promo_length = 3
    type = "Calculated"
    event_name = "Hot Sale"
    event_description = None
    event_status = "finished"
    dates_short = LazyAttribute(
        lambda o: f"{o.date_from.strftime('%b %d')} {o.date_to.strftime('%b %d')}"
    )
    days_since_previous_discount = 14
    event_day_number = 0.0
    event_id = LazyAttribute(
        lambda o: f"{o.event_name.lower()}:{o.date_from.strftime('%Y-%m-%d')}"
    )

    @classmethod
    def build_table(cls, size: int = 1, **kwargs) -> LegacyFactEventDayTable:
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls._converted_rows_to_table(raw_rows)

    @classmethod
    def build_df_batch(cls, size, **kwargs) -> pl.DataFrame:
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls._converted_rows_to_table(raw_rows).df

    @staticmethod
    def _converted_rows_to_table(
        converted_rows: list[dict],
    ) -> LegacyFactEventDayTable:
        return LegacyFactEventDayTable(df=pl.DataFrame(converted_rows))


@pytest.fixture
def legacy_fact_event_day_factory() -> type[LegacyFactEventDayFactory]:
    LegacyFactEventDayFactory.reset_sequence(force=True)
    return LegacyFactEventDayFactory


class ObservationDiscountsFactory(DictFactory):
    class Params:
        input = STEAM_PC_GLOBAL

    create_time = LazyFunction(lambda: pendulum.datetime(2024, 6, 13))
    update_time = LazyFunction(lambda: pendulum.datetime(2024, 6, 13))
    platform = LazyAttribute(lambda o: o.input["platform"])
    region = LazyAttribute(lambda o: o.input["region"])
    portal = LazyAttribute(lambda o: o.input["portal"])
    event_name = "True Hot Sale"
    datetime_from = LazyFunction(lambda: pendulum.datetime(2024, 6, 13))
    datetime_to = LazyFunction(lambda: pendulum.datetime(2024, 6, 20))
    is_event_joined = True
    discount_depth = 20
    unique_event_id = LazyAttribute(
        lambda o: f"{o.event_name.lower()}:{o.datetime_from.strftime('%Y-%m-%d')}"
    )
    base_event_id = Iterator(["60022", "60025", "60029"])
    group_id = Iterator(["60022", "60025", "60029"])
    base_sku_id = Iterator(["10000", "10001", "10002"])
    source_specific_discount_sku_id = Iterator(["60000", "60001", "60002"])
    sales_unique_sku_id = Iterator(["10000", "10001", "10002"])
    triggers_cooldown = True
    major = False
    discount_type = "custom"
    max_discount_percentage = 25
    price_increase_time = LazyFunction(lambda: pendulum.datetime(2024, 6, 20))
    promo_length = 7
    studio_id = 13
    unique_sku_id = LazyAttribute(
        lambda o: f"{o.base_sku_id}-{o.input['portal'].lower()}:{o.studio_id}"
    )
    report_id = 60022
    portal_platform_region = LazyAttribute(
        lambda o: f"{o.input['portal']}:{o.input['platform']}:{o.input['region']}"
    )

    @classmethod
    def build_table(cls, size: int = 1, **kwargs) -> ObservationDiscountsTable:
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls._converted_rows_to_table(raw_rows)

    @classmethod
    def build_df_batch(cls, size, **kwargs) -> pl.DataFrame:
        raw_rows = cls.build_batch(size=size, **kwargs)
        return cls._converted_rows_to_table(raw_rows).df

    @staticmethod
    def _converted_rows_to_table(
        converted_rows: list[dict],
    ) -> ObservationDiscountsTable:
        return ObservationDiscountsTable(df=pl.DataFrame(converted_rows))


@pytest.fixture
def observation_discounts_factory() -> type[ObservationDiscountsFactory]:
    ObservationDiscountsFactory.reset_sequence(force=True)
    return ObservationDiscountsFactory


@pytest.fixture
def legacy_fact_event_day():
    """Create a test fixture for LegacyFactEventDayTable."""
    now = pendulum.now()
    df = pl.DataFrame(
        {
            "date": [pendulum.date(2023, 1, 1), pendulum.date(2023, 1, 2)],
            "unique_sku_id": ["sku1", "sku2"],
            "studio_id": [1, 2],
            "discount": [0.2, 0.3],
            "date_from": [pendulum.date(2023, 1, 1), pendulum.date(2023, 1, 2)],
            "date_to": [pendulum.date(2023, 1, 5), pendulum.date(2023, 1, 6)],
            "dates_short": ["Jan 01 Jan 05", "Jan 02 Jan 06"],
            "promo_length": [5, 5],
            "event_status": ["ongoing", "ongoing"],
            "days_since_previous_discount": [0, 0],
            "event_name": ["Event 1", "Event 2"],
            "event_id": ["event1", "event2"],
            "type": ["Raw", "Raw"],
            "event_description": [None, None],
            "event_day_number": [0.0, 0.0],
            "portal_platform_region_id": [101001, 101002],
            "create_time": [now, now],
            "update_time": [now, now],
            "platform": ["PC", "PC"],
            "region": ["Global", "PS America"],
            "portal": ["Steam", "Steam"],
            "is_event_joined": [True, False],
            "base_event_id": ["base_event1", "base_event2"],
        }
    )
    return LegacyFactEventDayTable(df=df)


@pytest.fixture
def observation_discounts():
    """Create a test fixture for ObservationDiscountsTable."""
    now = pendulum.now()
    df = pl.DataFrame(
        {
            "datetime_from": [
                pendulum.datetime(2023, 1, 10),
                pendulum.datetime(2023, 1, 15),
            ],
            "datetime_to": [
                pendulum.datetime(2023, 1, 15),
                pendulum.datetime(2023, 1, 20),
            ],
            "discount_depth": [20, 30],
            "studio_id": [1, 2],
            "unique_sku_id": ["sku3", "sku4"],
            "event_name": ["Event 3", "Event 4"],
            "unique_event_id": ["event3", "event4"],
            "portal_platform_region": [
                "Nintendo:Switch:Nintendo America",
                "Nintendo:Switch:Nintendo Europe",
            ],
            "create_time": [now, now],
            "update_time": [now, now],
            "platform": ["PC", "PC"],
            "region": ["Global", "PS Europe"],
            "portal": ["Steam", "Steam"],
            "is_event_joined": [True, False],
            "base_event_id": ["base_event3", "base_event4"],
            "group_id": [1001, 1002],
            "base_sku_id": ["base_sku3", "base_sku4"],
            "source_specific_discount_sku_id": [5, 5],
            "triggers_cooldown": [True, False],
            "major": [True, False],
            "discount_type": ["custom", "store"],
            "max_discount_percentage": [25, 35],
            "price_increase_time": [
                pendulum.datetime(2023, 1, 15),
                pendulum.datetime(2023, 1, 20),
            ],
            "promo_length": [6, 6],
            "report_id": [5001, 5002],
            "sales_unique_sku_id": ["base_sku3", "base_sku4"],
        }
    )
    return ObservationDiscountsTable(df=df)


@pytest.fixture
def legacy_dim_skus():
    """Create a test fixture for LegacyDimSKUsTable."""
    now = pendulum.now()
    df = pl.DataFrame(
        {
            "sku_studio": ["sku1", "sku2", "sku3", "sku4"],
            "product_id": [101, 102, 103, 104],
            "product_name": ["Product 1", "Product 2", "Product 3", "Product 4"],
            "create_time": [now, now, now, now],
            "update_time": [now, now, now, now],
            "platform": ["PC", "PC", "PC", "PC"],
            "region": ["Global", "PS America", "PS Europe", "PS Asia"],
            "portal": ["Steam", "Steam", "Steam", "Steam"],
            "is_event_joined": [True, False, True, False],
            "base_event_id": [
                "base_event1",
                "base_event2",
                "base_event3",
                "base_event4",
            ],
            "base_sku_id": ["base_sku1", "base_sku2", "base_sku3", "base_sku4"],
            "human_name": ["Product 1", "Product 2", "Product 3", "Product 4"],
            "store_id": ["store1", "store2", "store3", "store4"],
            "studio_id": [1, 2, 1, 2],
            "portal_platform_region": [
                "Nintendo:Switch:Nintendo America",
                "Nintendo:Switch:Nintendo Europe",
                "Nintendo:Switch:Nintendo America",
                "Nintendo:Switch:Nintendo Europe",
            ],
            "human_name_indicator": [True, True, True, True],
            "sku_type": ["Game", "Game", "DLC", "Game"],
            "product_type": ["Game", "Game", "DLC", "Game"],
            "portal_platform_region_id": [101001, 101002, 101003, 101004],
            "package_name": ["package1", "package2", "package3", "package4"],
            "custom_group": [None, None, None, None],
            "ratio": [1.0, 1.0, 1.0, 1.0],
            "gso": [False, False, False, False],
            "is_baseline_precalculated": [False, False, False, False],
        }
    )
    return LegacyDimSKUsTable(df=df)


@pytest.fixture
def legacy_fact_sales_event_day():
    """Create a test fixture for LegacyFactSalesTable for event day tests."""
    now = pendulum.now()
    df = pl.DataFrame(
        {
            "date": [
                pendulum.date(2023, 1, 1),
                pendulum.date(2023, 1, 2),
                pendulum.date(2023, 1, 10),
                pendulum.date(2023, 1, 15),
            ],
            "sku_studio": ["sku1", "sku2", "sku3", "sku4"],
            "gross_sales": [100, 200, 150, 250],
            "create_time": [now, now, now, now],
            "update_time": [now, now, now, now],
            "platform": ["PC", "PC", "PC", "PC"],
            "region": ["Global", "PS America", "PS Europe", "PS Asia"],
            "portal": ["Steam", "Steam", "Steam", "Steam"],
            "is_event_joined": [True, False, True, False],
            "base_event_id": [
                "base_event1",
                "base_event2",
                "base_event3",
                "base_event4",
            ],
            "country_code": ["US", "US", "UK", "JP"],
            "currency_code": ["USD", "USD", "GBP", "JPY"],
            "studio_id": [1, 2, 1, 2],
            "bundle_name": [
                "Direct Package Sale",
                "Direct Package Sale",
                "Direct Package Sale",
                "Direct Package Sale",
            ],
            "portal_platform_region": [
                "Nintendo:Switch:Nintendo America",
                "Nintendo:Switch:Nintendo Europe",
                "Nintendo:Switch:Nintendo America",
                "Nintendo:Switch:Nintendo Europe",
            ],
            "portal_platform_region_id": [101001, 101002, 101003, 101004],
            "product_id": [101, 102, 103, 104],
            "hash_acquisition_properties": ["hash1", "hash2", "hash3", "hash4"],
            "date_sku_studio": [
                "2023-01-01_sku1",
                "2023-01-02_sku2",
                "2023-01-10_sku3",
                "2023-01-15_sku4",
            ],
            "source_file_id": [2001, 2002, 2003, 2004],
            "retailer_tag": [
                "Direct Package Sale",
                "Direct Package Sale",
                "Direct Package Sale",
                "Direct Package Sale",
            ],
            "base_price_local": [50.0, 60.0, 40.0, 70.0],
            "calculated_base_price_usd": [50.0, 60.0, 50.0, 0.5],
            "net_sales": [90, 180, 130, 220],
            "gross_returned": [0, 0, 0, 0],
            "units_returned": [0, 0, 0, 0],
            "units_sold": [2, 4, 3, 5],
            "free_units": [0, 0, 0, 0],
            "price_local": [45.0, 50.0, 35.0, 65.0],
            "price_usd": [45.0, 50.0, 43.75, 0.46],
            "net_sales_approx": [90, 180, 130, 220],
            "category": ["Game", "Game", "DLC", "Game"],
            "calculated_base_price_local_v2": [50.0, 60.0, 40.0, 70.0],
            "calculated_base_price_usd_v2": [50.0, 60.0, 50.0, 0.5],
        }
    )
    return LegacyFactSalesTable(df=df)


@pytest.fixture
def legacy_fact_sales_cumulative():
    """Create a test fixture for LegacyFactSalesTable for cumulative tests with time series data."""
    now = pendulum.now()
    dates = [
        pendulum.date(2023, 1, 1),
        pendulum.date(2023, 1, 2),
        pendulum.date(2023, 1, 3),
        pendulum.date(2023, 1, 4),
    ]

    df = pl.DataFrame(
        {
            "date": dates,
            "sku_studio": ["sku1", "sku1", "sku2", "sku2"],
            "gross_sales": [100, 150, 120, 180],
            "create_time": [now] * 4,
            "update_time": [now] * 4,
            "platform": ["PC"] * 4,
            "region": ["Global", "Global", "PS America", "PS America"],
            "portal": ["Steam"] * 4,
            "is_event_joined": [True, True, False, False],
            "base_event_id": [
                "base_event1",
                "base_event1",
                "base_event2",
                "base_event2",
            ],
            "country_code": ["US", "US", "US", "US"],
            "currency_code": ["USD", "USD", "USD", "USD"],
            "studio_id": [1, 1, 2, 2],
            "bundle_name": ["Direct Package Sale"] * 4,
            "portal_platform_region": ["Nintendo:Switch:Nintendo America"] * 2
            + ["Nintendo:Switch:Nintendo Europe"] * 2,
            "portal_platform_region_id": [101001, 101001, 101002, 101002],
            "product_id": [101, 101, 102, 102],
            "hash_acquisition_properties": ["hash1", "hash2", "hash3", "hash4"],
            "date_sku_studio": [
                f"{d}_{s}" for d, s in zip(dates, ["sku1", "sku1", "sku2", "sku2"])
            ],
            "source_file_id": list(range(2001, 2005)),
            "retailer_tag": ["Direct Package Sale"] * 4,
            "base_price_local": [50.0, 50.0, 60.0, 60.0],
            "calculated_base_price_usd": [50.0, 50.0, 60.0, 60.0],
            "net_sales": [90, 135, 100, 160],
            "gross_returned": [0, 5, 0, 5],
            "units_returned": [0, 1, 0, 1],
            "units_sold": [2, 3, 2, 3],
            "free_units": [0, 1, 0, 0],
            "price_local": [45.0, 45.0, 50.0, 50.0],
            "price_usd": [45.0, 45.0, 50.0, 50.0],
            "net_sales_approx": [90, 135, 100, 160],
            "category": ["Game", "Game", "Game", "Game"],
            "calculated_base_price_local_v2": [50.0, 50.0, 60.0, 60.0],
            "calculated_base_price_usd_v2": [50.0, 50.0, 60.0, 60.0],
        }
    )
    return LegacyFactSalesTable(df=df)


@pytest.fixture
def mock_legacy_dim_skus():
    data = {
        "base_sku_id": [1, 1, 2, 2, 3],
        "human_name": ["Product A", "Product A", "Product B", "Product B", "Product C"],
        "store_id": [10, 10, 20, 20, 30],
        "studio_id": [5] * 5,
        "portal_platform_region": ["ham", "ham", "spam", "spam", "eggs"],
        "human_name_indicator": ["sales", "visibility", "sales", "visibility", "sales"],
        "sku_type": ["SALES", "STORE", "SALES", "STORE", "SALES"],
        "product_name": [
            "Product A",
            "Product A",
            "__IGNORE",
            "__IGNORE",
            "Product C",
        ],
        "product_type": [None, None, "type1", "type2", "type3"],
        "sku_studio": ["sku1", "sku2", "sku3", "sku4", "sku5"],
        "portal_platform_region_id": [10, 10, 20, 20, 30],
        "product_id": ["1", "1", "2", "2", "3"],
        "package_name": [None] * 5,
        "custom_group": [None] * 5,
        "ratio": [1.0] * 5,
        "gso": [5, 7, 6, 8, 9],
        "is_baseline_precalculated": [True, False, True, False, True],
    }
    return pl.DataFrame(data)


@pytest.fixture
def mock_legacy_fact_sales():
    data = {
        "country_code": ["USA"] * 5,
        "currency_code": ["USD"] * 5,
        "studio_id": [5] * 5,
        "sku_studio": ["sku1", "sku1", "sku2", "sku2", "sku3"],
        "bundle_name": ["Bundle A"] * 5,
        "portal_platform_region": ["ham", "ham", "spam", "spam", "eggs"],
        "portal_platform_region_id": [10, 10, 20, 20, 30],
        "product_id": ["1", "1", "2", "2", "3"],
        "hash_acquisition_properties": ["hash1", "hash2", "hash3", "hash4", "hash5"],
        "date": [date(2021, 1, 1)] * 5,
        "date_sku_studio": [
            "2021-01-01:sku1",
            "2021-01-01:sku2",
            "2021-01-01:sku3",
            "2021-01-01:sku4",
            "2021-01-01:sku5",
        ],
        "source_file_id": [128] * 5,
        "retailer_tag": ["NOT APPLICABLE"] * 5,
        "base_price_local": [None] * 5,
        "calculated_base_price_usd": [None] * 5,
        "net_sales": [0.0] * 5,
        "gross_returned": [0.0] * 5,
        "gross_sales": [0.0] * 5,
        "units_returned": [0] * 5,
        "units_sold": [0] * 5,
        "free_units": [0] * 5,
        "price_local": [0.0] * 5,
        "price_usd": [0.0] * 5,
        "net_sales_approx": [0.0] * 5,
        "category": ["Free"] * 5,
        "calculated_base_price_local_v2": [None] * 5,
        "calculated_base_price_usd_v2": [None] * 5,
    }

    return pl.DataFrame(data)


@pytest.fixture
def mock_legacy_dim_portals():
    data = {
        "portal": ["portal1", "portal2", "portal3"],
        "platform": ["platform1", "platform2", "platform3"],
        "region": ["region1", "region2", "region3"],
        "store": ["store1", "store2", "store3"],
        "abbreviated_name": ["a", "b", "c"],
        "portal_platform_region": [
            "portal1_platform1_region1",
            "portal2_platform2_region2",
            "portal3_platform3_region3",
        ],
        "portal_platform_region_id": [10, 20, 30],
        "so_portal": [10, 7, 1],
        "so_platform": [1, 0, 0],
        "so_region": [0, 0, 0],
        "pso": [1010, 700, 100],
    }

    return pl.DataFrame(data)


@pytest.fixture
def mock_legacy_fact_event_day():
    data = {
        "discount": [0.25] * 5,
        "date": [date(2021, 1, 1)] * 5,
        "studio_id": [5] * 5,
        "unique_sku_id": ["sku1", "sku2", "sku3", "sku4", "sku5"],
        "portal_platform_region_id": [10, 20, 30, 40, 50],
        "date_from": [date(2021, 1, 1)] * 5,
        "date_to": [date(2021, 1, 3)] * 5,
        "promo_length": [3] * 5,
        "type": ["calculated"] * 5,
        "event_name": ["winter sale"] * 5,
        "event_description": [None] * 5,
        "event_status": ["finished"] * 5,
        "dates_short": ["Jan 01 Jan 03"] * 5,
        "days_since_previous_discount": [21] * 5,
        "event_day_number": [1.0, 0.83, 0.67, 0.5, 0.33],
        "event_id": ["event1"] * 5,
    }

    return pl.DataFrame(data)


@pytest.fixture
def mock_legacy_fact_visibility():
    data = {
        "poertal_platform_region": ["ham", "ham", "spam", "spam", "eggs"],
        "portal_platform_region_id": [10, 20, 30, 40, 50],
        "product_id": ["1", "2", "3", "4", "5"],
        "sku_studio": ["sku1", "sku2", "sku3", "sku4", "sku5"],
        "date": [date(2021, 1, 1)] * 5,
        "studio_id": [5] * 5,
        "date_sku_studio": [
            "2021-01-01:sku1",
            "2021-01-01:sku2",
            "2021-01-01:sku3",
            "2021-01-01:sku4",
            "2021-01-01:sku5",
        ],
        "hash_traffic_source": ["hash1", "hash2", "hash3", "hash4", "hash5"],
        "date_product_studio": [
            "2021-01-01:1:5",
            "2021-01-01:2:5",
            "2021-01-01:3:5",
            "2021-01-01:4:5",
            "2021-01-01:5:5",
        ],
        "visits": [1, 2, 3, 4, 5],
        "owner_visits": [11, 12, 13, 14, 15],
        "impressions": [6, 7, 8, 9, 10],
        "owner_impressions": [16, 17, 18, 19, 20],
        "navigation": ["Direct Navigation"] * 5,
    }

    return pl.DataFrame(data)


@pytest.fixture
def mock_legacy_fact_wishlist_actions():
    data = {
        "poertal_platform_region": ["ham", "ham", "spam", "spam", "eggs"],
        "portal_platform_region_id": [10, 20, 30, 40, 50],
        "product_id": ["1", "2", "3", "4", "5"],
        "sku_studio": ["sku1", "sku2", "sku3", "sku4", "sku5"],
        "date": [date(2021, 1, 1)] * 5,
        "studio_id": [5] * 5,
        "date_sku_studio": [
            "2021-01-01:sku1",
            "2021-01-01:sku2",
            "2021-01-01:sku3",
            "2021-01-01:sku4",
            "2021-01-01:sku5",
        ],
        "date_product_studio": [
            "2021-01-01:1:5",
            "2021-01-01:2:5",
            "2021-01-01:3:5",
            "2021-01-01:4:5",
            "2021-01-01:5:5",
        ],
        "adds": [11, 12, 13, 14, 15],
        "deletes": [16, 17, 18, 19, 20],
        "purchases_and_activations": [21, 22, 23, 24, 25],
        "gifts": [26, 27, 28, 29, 30],
        "country_code": ["USA"] * 5,
    }

    return pl.DataFrame(data)


@pytest.fixture
def mock_legacy_fact_wishlist_cohorts():
    data = {
        "portal_platform_region": ["ham", "ham", "spam", "spam", "eggs"],
        "portal_platform_region_id": [10, 20, 30, 40, 50],
        "product_id": ["1", "2", "3", "4", "5"],
        "sku_studio": ["sku1", "sku2", "sku3", "sku4", "sku5"],
        "date": [date(2021, 1, 1)] * 5,
        "studio_id": [5] * 5,
        "date_sku_studio": [
            "2021-01-01:sku1",
            "2021-01-01:sku2",
            "2021-01-01:sku3",
            "2021-01-01:sku4",
            "2021-01-01:sku5",
        ],
        "date_product_studio": [
            "2021-01-01:1:5",
            "2021-01-01:2:5",
            "2021-01-01:3:5",
            "2021-01-01:4:5",
            "2021-01-01:5:5",
        ],
        "month_cohort": ["2021-01-01"] * 5,
        "total_conversions": [1, 2, 3, 4, 5],
        "purchases_and_activations": [6, 7, 8, 9, 10],
        "gifts": [11, 12, 13, 14, 15],
    }

    return pl.DataFrame(data)


@pytest.fixture
def mock_observation_discounts():
    return pl.DataFrame()
