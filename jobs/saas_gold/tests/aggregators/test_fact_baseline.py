import polars as pl
import pytest

from data_sdk.domain import DisplayPortal
from saas_gold.aggregators.fact_baseline import (
    FactBaselineAggregator,
    _prepare_fact_sales,
)


def test_empty_input_data(
    legacy_skus_factory,
    legacy_fact_event_day_factory,
    observation_discounts_factory,
    observation_sales_factory,
    silver_skus_factory,
):
    legacy_dim_skus_table = legacy_skus_factory.build_table()
    observation_sales_table = observation_sales_factory.build_table(size=0)
    legacy_fact_event_day_table = legacy_fact_event_day_factory.build_table()
    observation_discounts_table = observation_discounts_factory.build_table()
    silver_skus_table = silver_skus_factory.build_table()

    result = FactBaselineAggregator(
        legacy_dim_skus_table,
        legacy_fact_event_day_table,
        observation_discounts_table,
        observation_sales_table,
        silver_skus_table,
    ).aggregate()

    assert result.df.is_empty()


@pytest.fixture
def sample_sales_data():
    """Sample sales data covering different scenarios"""
    return pl.LazyFrame(
        {
            "category": [
                "Sale",
                "Free & Sale",
                "Return",
                "Retail Store",
                "Retail Online",
                "Non-billable Demo",
                "Non-billable Test",
                "Other Category",
                "Invalid Sale",
                "Free & Return",
            ],
            "units_sold": [100, 50, 25, 200, 150, 75, 30, 40, 60, 20],
            "net_sales": [
                1000.0,
                500.0,
                250.0,
                2000.0,
                1500.0,
                750.0,
                300.0,
                400.0,
                600.0,
                200.0,
            ],
            "portal": [
                DisplayPortal.EPIC,
                DisplayPortal.STEAM,
                DisplayPortal.EPIC,
                DisplayPortal.STEAM,
                DisplayPortal.EPIC,
                DisplayPortal.STEAM,
                DisplayPortal.EPIC,
                DisplayPortal.STEAM,
                DisplayPortal.STEAM,
                DisplayPortal.EPIC,
            ],
        }
    )


@pytest.fixture
def empty_sales_data():
    """Empty sales data for edge case testing"""
    return pl.LazyFrame(
        {"category": [], "units_sold": [], "net_sales": [], "portal": []}
    )


@pytest.fixture
def edge_case_data():
    """Edge cases with None/null values"""
    return pl.LazyFrame(
        {
            "category": ["Sale", None, "Retail Store"],
            "units_sold": [100, 50, None],
            "net_sales": [1000.0, None, 1500.0],
            "portal": [DisplayPortal.EPIC, DisplayPortal.STEAM, None],
        }
    )


def test_units_sold_directly_calculation(sample_sales_data):
    """Test units_sold_directly calculation for sold directly categories"""
    result = _prepare_fact_sales(sample_sales_data).collect()

    # Check sold directly categories
    sold_directly_mask = result["category"].is_in(
        [
            "Free & Sale",
            "Free & Return",
            "Sale",
            "Invalid Sale",
            "Return",
            "Invalid Return",
        ]
    )

    # Where category is in sold_directly_categories, units_sold_directly should equal units_sold
    directly_sold_rows = result.filter(sold_directly_mask)
    assert (
        directly_sold_rows["units_sold_directly"] == directly_sold_rows["units_sold"]
    ).all()

    # Where category is NOT in sold_directly_categories, units_sold_directly should be 0
    not_directly_sold_rows = result.filter(~sold_directly_mask)
    assert (not_directly_sold_rows["units_sold_directly"] == 0).all()


def test_units_sold_retail_calculation(sample_sales_data):
    """Test units_sold_retail calculation for retail categories"""
    result = _prepare_fact_sales(sample_sales_data).collect()

    # Check retail categories (starts with "Retail")
    retail_mask = result["category"].str.starts_with("Retail")

    # Where category starts with "Retail", units_sold_retail should equal units_sold
    retail_rows = result.filter(retail_mask)
    assert (retail_rows["units_sold_retail"] == retail_rows["units_sold"]).all()

    # Where category does NOT start with "Retail", units_sold_retail should be 0
    non_retail_rows = result.filter(~retail_mask)
    assert (non_retail_rows["units_sold_retail"] == 0).all()


def test_units_sold_non_billable_calculation(sample_sales_data):
    """Test units_sold_non_billable calculation for non-billable categories"""
    result = _prepare_fact_sales(sample_sales_data).collect()

    # Check non-billable categories (starts with "Non-billable")
    non_billable_mask = result["category"].str.starts_with("Non-billable")

    # Where category starts with "Non-billable", units_sold_non_billable should equal units_sold
    non_billable_rows = result.filter(non_billable_mask)
    assert (
        non_billable_rows["units_sold_non_billable"] == non_billable_rows["units_sold"]
    ).all()

    # Where category does NOT start with "Non-billable", units_sold_non_billable should be 0
    billable_rows = result.filter(~non_billable_mask)
    assert (billable_rows["units_sold_non_billable"] == 0).all()


def test_net_sales_approx_calculation(sample_sales_data):
    """Test net_sales_approx calculation based on portal"""
    result = _prepare_fact_sales(sample_sales_data).collect()

    epic_rows = result.filter(pl.col("portal") == DisplayPortal.EPIC.value)
    expected_epic = (epic_rows["net_sales"] * 0.88).round(2).rename("net_sales_approx")
    assert epic_rows["net_sales_approx"].round(2).series_equal(expected_epic)

    non_epic_rows = result.filter(pl.col("portal") != DisplayPortal.EPIC.value)
    expected_non_epic = (
        (non_epic_rows["net_sales"] * 0.7).round(2).rename("net_sales_approx")
    )
    assert non_epic_rows["net_sales_approx"].round(2).series_equal(expected_non_epic)


def test_all_columns_added(sample_sales_data):
    """Test that all expected new columns are added"""
    original_columns = sample_sales_data.columns
    result = _prepare_fact_sales(sample_sales_data).collect()

    expected_new_columns = [
        "units_sold_directly",
        "units_sold_retail",
        "units_sold_non_billable",
        "net_sales_approx",
    ]

    for col in expected_new_columns:
        assert col in result.columns

    # Original columns should still be present
    for col in original_columns:
        assert col in result.columns


def test_empty_dataframe(empty_sales_data):
    """Test function works with empty dataframe"""
    result = _prepare_fact_sales(empty_sales_data).collect()

    # Should have all the new columns
    expected_columns = [
        "category",
        "units_sold",
        "net_sales",
        "portal",
        "units_sold_directly",
        "units_sold_retail",
        "units_sold_non_billable",
        "net_sales_approx",
    ]

    assert result.columns == expected_columns
    assert len(result) == 0


def test_edge_cases_with_nulls(edge_case_data):
    """Test function handles null values appropriately"""
    result = _prepare_fact_sales(edge_case_data).collect()

    # Should not raise errors and should have expected shape
    assert len(result) == 3
    assert "units_sold_directly" in result.columns
    assert "units_sold_retail" in result.columns
    assert "units_sold_non_billable" in result.columns
    assert "net_sales_approx" in result.columns


def test_specific_category_examples():
    """Test specific examples of each category type"""
    test_data = pl.LazyFrame(
        {
            "category": ["Sale", "Retail Store", "Non-billable Demo", "Other"],
            "units_sold": [100, 200, 75, 50],
            "net_sales": [1000.0, 2000.0, 750.0, 500.0],
            "portal": [
                DisplayPortal.EPIC,
                DisplayPortal.STEAM,
                DisplayPortal.EPIC,
                DisplayPortal.STEAM,
            ],
        }
    )

    result = _prepare_fact_sales(test_data).collect()

    print(result)

    # Test specific values
    assert (
        result[0, "units_sold_directly"] == 100
    )  # "Sale" is in sold_directly_categories
    assert result[0, "units_sold_retail"] == 0  # "Sale" doesn't start with "Retail"
    assert (
        result[0, "units_sold_non_billable"] == 0
    )  # "Sale" doesn't start with "Non-billable"
    assert result[0, "net_sales_approx"] == 880.0  # EPIC portal: 1000 * 0.88

    assert (
        result[1, "units_sold_directly"] == 0
    )  # "Retail Store" not in sold_directly_categories
    assert result[1, "units_sold_retail"] == 200  # "Retail Store" starts with "Retail"
    assert (
        result[1, "units_sold_non_billable"] == 0
    )  # "Retail Store" doesn't start with "Non-billable"
    assert result[1, "net_sales_approx"] == 1400.0  # STEAM portal: 2000 * 0.7


def test_case_sensitivity():
    """Test that category matching is case sensitive"""
    test_data = pl.LazyFrame(
        {
            "category": [
                "sale",
                "RETAIL Store",
                "non-billable Demo",
            ],  # lowercase/different case
            "units_sold": [100, 200, 75],
            "net_sales": [1000.0, 2000.0, 750.0],
            "portal": [DisplayPortal.EPIC, DisplayPortal.STEAM, DisplayPortal.EPIC],
        }
    )

    result = _prepare_fact_sales(test_data).collect()

    # Should not match due to case sensitivity
    assert result[0, "units_sold_directly"] == 0  # "sale" != "Sale"
    assert (
        result[1, "units_sold_retail"] == 0
    )  # "RETAIL Store" doesn't start with "Retail"
    assert (
        result[2, "units_sold_non_billable"] == 0
    )  # "non-billable Demo" doesn't start with "Non-billable"


@pytest.mark.parametrize(
    "category,expected_directly",
    [
        ("Free & Sale", True),
        ("Free & Return", True),
        ("Sale", True),
        ("Invalid Sale", True),
        ("Return", True),
        ("Invalid Return", True),
        ("Other Category", False),
        ("Retail Something", False),
    ],
)
def test_sold_directly_categories_parametrized(category, expected_directly):
    """Parametrized test for sold directly categories"""
    test_data = pl.LazyFrame(
        {
            "category": [category],
            "units_sold": [100],
            "net_sales": [1000.0],
            "portal": [DisplayPortal.EPIC],
        }
    )

    result = _prepare_fact_sales(test_data).collect()

    if expected_directly:
        assert result[0, "units_sold_directly"] == 100
    else:
        assert result[0, "units_sold_directly"] == 0
