import json


def test_mock_csv_file(mock_csv_file):
    headers = ["foo", "bar", "baz"]
    content = [
        {
            "foo": "some string",
            "bar": 'some string with "quotes"',
            "baz": "special,case,with,commas",
        },
        {
            "foo": 123147,
            "bar": 323842,
            "baz": 1548171050,
        },
    ]

    csv_content = mock_csv_file(headers, content)

    assert csv_content == (
        "foo,bar,baz\r\n"
        'some string,"some string with ""quotes""","special,case,with,commas"\r\n'
        "123147,323842,1548171050\r\n"
    )


def test_mock_zip_file(mock_csv_file, mock_zip_file):
    manifest_json_content = json.dumps({
        "dateFrom": "2010-01-01T00:00:00.000Z",
        "dateTo": "2023-09-26T00:00:00.000Z",
        "metadata": {
            "discounts_all_3204.csv": {
                "organization": "3204",
                "dateFrom": "2010-01-01T00:00:00.000Z",
                "dateTo": "2023-09-26T00:00:00.000Z",
                "rawData": True,
            },
            "discounts_all_123147.csv": {
                "organization": "123147",
                "dateFrom": "2010-01-01T00:00:00.000Z",
                "dateTo": "2023-09-26T00:00:00.000Z",
                "rawData": True,
            },
            "discountManagementData.json": {
                "dateFrom": "2010-01-01T00:00:00.000Z",
                "dateTo": "2023-09-26T00:00:00.000Z",
                "rawData": True,
            },
        },
    })

    price_increase_times_csv_content = mock_csv_file(
        ["publisherId", "packageId", "priceIncreaseTime"],
        [
            {
                "publisherId": 3204,
                "packageId": 165761,
                "priceIncreaseTime": 1682951047,
            },
            {
                "publisherId": 123147,
                "packageId": 323842,
                "priceIncreaseTime": 1548171050,
            },
            {"publisherId": 123147, "packageId": 711996, "priceIncreaseTime": 0},
        ],
    )

    zip_content = {
        "manifest.json": manifest_json_content,
        "priceIncreaseTimes.csv": price_increase_times_csv_content,
    }

    with mock_zip_file(zip_content) as zip_file:
        number_of_files_in_zip = len(zip_file.namelist())

        assert number_of_files_in_zip == 2

        assert zip_file.read("manifest.json") == manifest_json_content.encode("utf-8")
        assert zip_file.read(
            "priceIncreaseTimes.csv"
        ) == price_increase_times_csv_content.encode("utf-8")


def test_csv_file_factory_abc(csv_file_factory):
    csv_file = csv_file_factory(1, headers=["a", "b", "c"])

    assert csv_file == ("a,b,c\r\n1,2,3\r\n")


def test_csv_file_factory_wxyz(csv_file_factory):
    csv_file = csv_file_factory(1, headers=["w", "x", "y", "z"])

    assert csv_file == ("w,x,y,z\r\n1,2,3,4\r\n")


def test_csv_file_factory_abc_multiple_rows(csv_file_factory):
    csv_file = csv_file_factory(1, headers=["a", "b", "c"], rows=3)

    assert csv_file == ("a,b,c\r\n1,2,3\r\n4,5,6\r\n7,8,9\r\n")
