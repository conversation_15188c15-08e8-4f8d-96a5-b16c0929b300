#!/bin/bash

# Path to the jobs directory
JOBS_DIR="jobs"

# Function to update pipeline-sdk dependency and regenerate poetry.lock
update_pipeline_sdk() {
    local project_dir="$1"

    if ! grep -q 'pipeline-sdk' "$project_dir/pyproject.toml"; then
        echo "Skipping $project_dir as it does not use pipeline-sdk"
        return
    fi

    echo "Updating pipeline-sdk in $project_dir"

    cd "$project_dir"
    poetry add --source indiebi pipeline-sdk@latest

    # Check if the lock file was generated successfully
    if [[ $? -eq 0 ]]; then
        echo "Successfully updated pipeline-sdk and regenerated poetry.lock in $project_dir"
    else
        echo "Failed to update pipeline-sdk in $project_dir"
    fi

    # Change back to the original directory
    cd - > /dev/null

}

# Iterate over each folder in the jobs directory
for project in "$JOBS_DIR"/*; do
    if [[ -d "$project" ]]; then
        update_pipeline_sdk "$project"
    fi
done
