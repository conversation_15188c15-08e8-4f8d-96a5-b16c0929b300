<!--- docs
# Metadata used by our doc generator
title: data-jobs
group: data-jobs
-->
# data-jobs

This repository hosts data processing applications deployed to Kubernetes as KEDA jobs, using GitLab CI/CD.

## Setup & Development

All data jobs in this repo are Python Poetry projects. The projects are separate and should be using separate virtual environments.

Set it up for local development using `poetry install`.
Run tests for each job by running `poetry run pytest`.

Each job's own README.md might contain more detailed information.


### Local setup
In order to be able to run `poetry install` properly you will need to authorize in gitlab api.
To do that first create a proper personal access token in Gitlab and then run:
```bash
  poetry config keyring.enabled false #  Need for local image build
  poetry config http-basic.indiebi {USERNAME} {TOKEN_VALUE}
```

#### Hooks

If git hooks are prepared for a specific project, to install them, run `poe hooks` within the project's virtual environment. Details on how git hooks are set up in a multi-project git repository are explained [here](./.hooks/README.md).

#### MacOS-specific setup
When trying to access MSSQL DB on MacOS, you may encounter the following error:

```py
def connect(self, *cargs, **cparams):
# inherits the docstring from interfaces.Dialect.connect
>       return self.dbapi.connect(*cargs, **cparams)
E       sqlalchemy.exc.DBAPIError: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
E       (pyodbc.Error) ('01000', "[01000] [unixODBC][Driver Manager]Can't open lib 'ODBC Driver 17 for SQL Server' : file not found (0) (SQLDriverConnect)")
E       (Background on this error at: https://sqlalche.me/e/14/dbapi)

.venv/lib/python3.10/site-packages/sqlalchemy/engine/default.py:598: DBAPIError
```

You can install `msodbcsql17` using Homebrew:
```bash
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/master/install.sh)"
brew tap microsoft/mssql-release https://github.com/Microsoft/homebrew-mssql-release
brew update
HOMEBREW_ACCEPT_EULA=Y brew install msodbcsql17 mssql-tools
```
Check out [Microsoft docs](https://learn.microsoft.com/en-us/sql/connect/odbc/linux-mac/install-microsoft-odbc-driver-sql-server-macos?view=sql-server-ver15#17) for more details.


### Building Dockerfiles locally
The common Dockerfile has a lot of build arguments, so building it directly using `docker build` can be cumbersome.
To address that, there is a script called `common/docker-build.sh` that sets most of the build arguments for you.


Call the script from the repository root like this `./common/docker-build.sh <data_job_directory>`.
For the script to work, you need to have `poetry` authentication configured locally (see above). Specifically, `$HOME/.config/pypoetry/auth.toml` needs to
contain auth for `indiebi` source.
Refer to the source to see what arguments are configurable through env vars.

## CI/CD

Data Jobs from this repo have share a pipeline template `common/job-pipeline.yml`, which is included once per job in `.gitlab-ci.yml`.
The template is parametrized using inputs (see [Gitlab Docs](https://docs.gitlab.com/ee/ci/yaml/inputs.html)).

In the same way, all libs have a shared pipeline in `common/lib-pipeline.yml`


## Dockerfiles

Data Jobs from this repo share a common Dockerfile under `common/Dockerfile`. This file is parametrized and should meet most of your needs.
If you have different requirements, you can:
- use a different Dockerfile by specifying `job-dockerfile` input in `.gitlab-ci.yml`:
  ``` yaml
     - local: common/job-pipeline.yml
       inputs:
         job-dir: "complex_job"
         job-dockerfile: "jobs/complex_job/Dockerfile"
  ```
- add more arguments to `common/Dockerfile` and adjust `common/job-pipeline.yml` accordingly,
- if your job is completely different than what we're doing in this repo, consider putting your data job in another repo.

## How to add a new data job in this repo

1. Create a new directory in `jobs/`.
1. Initialize a new Poetry project with `poetry init`.
2. Update pyproject.toml with required pipeline-sdk (and other dependencies if needed)
1. For the common Dockerfile to work (see above), your data job code MUST be started by launching python -m <job_name>.main.
1. Include a `definition.json` file in the directory.
1. Include a README.md (can be empty).
1. Update CI/CD definition in `.gitlab-ci.yaml` 
1. Update `pipeline.json` to state when it should run
1. Add job-specific infrastructure in [single click repo](https://gitlab.com/bluebrick/indiebi/infra/single-click-indiebi/-/tree/main/modules/stacks/workload/dpt?ref_type=heads)
1. Add job declaration in [single click repo](https://gitlab.com/bluebrick/indiebi/infra/single-click-indiebi/-/tree/main/modules/stacks/workload/dpt?ref_type=heads)


## How to add a new lib
1. Create a new directory in `libs/`.
1. Initialize a new Poetry project.
1. Include a README.md (can be empty).


## Releasing LIB
To release a new library version, a new tag must be created either through the GitLab UI or the Git command line. Tags should be created with a descriptive release note detailing the changes or updates in this release.

### Creating a tag in GitLab UI
1. Navigate to the 'Code->Tags' in `data-jobs` project.
1. Click 'New tag'.
1. Enter the tag name (the version number, e.g., `1.0.3`).
1. Fill the message with proper release notes.
1. Click 'Create tag' to finalize the tag.

### Creating a tag using GIT
1. Ensure that you are on the `main` branch.
1. Create a tag and fill release notes in a editor of your preference (VIM):
```bash
  git tag -a 1.0.3
```
1. Push the tag to the remote repository
```bash
  git push origin 1.0.3
```

### Example of good release note
```
Features:
- Added new sorting functionality to the data processing module.

Fixes:
- Fixed a bug in the login flow that prevented users from resetting their passwords.

Other:
- Improved the performance of the API by optimizing database queries.
- Patched XSS vulnerability in the comment section.
- Update Pandas to 3.0.0 version
```
