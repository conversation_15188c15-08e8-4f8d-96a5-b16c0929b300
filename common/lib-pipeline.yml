spec:
  inputs:
    lib-dir:
    pytest-extra-args:
      default: ""
    python-version:
      default: "3.11"
    base-distro:
      default: "bullseye"
    poetry-image-tag:
      default: "prod"
---
.on-mr:
  rules:
    - if: $CI_MERGE_REQUEST_ID
      when: always
      allow_failure: false
      changes:
        - "libs/$LIB_DIR/**/*"
        - .gitlab-ci.yml
        - "common/**/*"

.manual-on-mr:
  rules:
    - if: $CI_MERGE_REQUEST_ID
      when: manual
      allow_failure: true
      changes:
        - "libs/$LIB_DIR/**/*"
        - .gitlab-ci.yml
        - "common/**/*"

.on-release:
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: always
      allow_failure: false
      changes:
        - "libs/$LIB_DIR/**/*"
        - .gitlab-ci.yml
        - "common/**/*"

.manual-on-release:
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: manual
      allow_failure: true
      changes:
        - "libs/$LIB_DIR/**/*"
        - .gitlab-ci.yml
        - "common/**/*"

"$[[ inputs.lib-dir ]]:lint":
  stage: lint
  image: crindiebimain.azurecr.io/dpt/ci-cd/python-poetry-$[[ inputs.python-version ]]-$[[ inputs.base-distro ]]:$[[ inputs.poetry-image-tag ]]
  variables:
    LIB_DIR: $[[ inputs.lib-dir ]]
    POETRY_CACHE_DIR: $CI_PROJECT_DIR/libs/$LIB_DIR/.poetry-cache
    POETRY_VIRTUALENVS_IN_PROJECT: "true"

  script:
    - cd ./libs/$LIB_DIR
    - poetry --version
    - poetry install
    - poetry run poe ci-lint
  rules:
    - !reference [.on-mr, rules]
  cache:
    key: global
    paths:
      - libs/$LIB_DIR/.poetry-cache

"$[[ inputs.lib-dir ]]:test":
  stage: test
  image: crindiebimain.azurecr.io/dpt/ci-cd/python-poetry-$[[ inputs.python-version ]]-$[[ inputs.base-distro ]]:$[[ inputs.poetry-image-tag ]]
  variables:
    LIB_DIR: $[[ inputs.lib-dir ]]
    POETRY_CACHE_DIR: $CI_PROJECT_DIR/libs/$LIB_DIR/.poetry-cache
    POETRY_VIRTUALENVS_IN_PROJECT: "true"

  script:
    - cd ./libs/$LIB_DIR
    - poetry --version
    - poetry install
    - poetry run poe ci-test $[[ inputs.pytest-extra-args ]]
  artifacts:
    when: always
    reports:
      junit: libs/$LIB_DIR/pytest.xml
  rules:
    - !reference [.on-mr, rules]
  cache:
    key: global
    paths:
      - libs/$LIB_DIR/.poetry-cache

"$[[ inputs.lib-dir ]]:deploy":
  stage: deploy
  image: crindiebimain.azurecr.io/dpt/ci-cd/python-poetry-$[[ inputs.python-version ]]-$[[ inputs.base-distro ]]:$[[ inputs.poetry-image-tag ]]
  variables:
    LIB_DIR: $[[ inputs.lib-dir ]]
    POETRY_CACHE_DIR: $CI_PROJECT_DIR/libs/$LIB_DIR/.poetry-cache
    POETRY_VIRTUALENVS_IN_PROJECT: "true"
  script:
    - export PACKAGE_NAME="${LIB_DIR//-/_}"
    - cd ./libs/$LIB_DIR
    - sed -i "s/version = \"0.0.0\"/version = \"${CI_COMMIT_TAG:-0.5.0}\"/" pyproject.toml
    - sed -i "s/__version__ = \"0.0.0\"/__version__ = \"${CI_COMMIT_TAG:-0.5.0}\"/" $PACKAGE_NAME/__init__.py
    - poetry build
    - poetry config repositories.gitlab "https://gitlab.com/api/v4/projects/$CI_PROJECT_ID/packages/pypi"
    - poetry config http-basic.gitlab gitlab-ci-token "$CI_JOB_TOKEN"
    - poetry publish --repository gitlab

  rules:
    - if: $CI_COMMIT_TAG
