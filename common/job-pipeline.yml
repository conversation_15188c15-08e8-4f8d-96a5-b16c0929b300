spec:
  inputs:
    job-dir:
    force-project-dir:
      default: ""
    job-dockerfile:
      default: "common/Dockerfile"
    pytest-extra-args:
      default: ""
    python-version:
      default: "3.10"
    base-distro:
      default: "bullseye"
    poetry-image-tag:
      default: "prod"
    builder-image-tag:
      default: "prod"
    extra-test-command:
      default: "echo 'no extra test commands'"
---
.job-on-mr:
  rules:
    - if: $CI_MERGE_REQUEST_ID
      when: always
      allow_failure: false
      changes:
        - "jobs/$JOB_DIR/**/*"
        - .gitlab-ci.yml
        - "common/**/*"

.job-manual-on-mr:
  rules:
    - if: $CI_MERGE_REQUEST_ID
      when: manual
      allow_failure: true
      changes:
        - "jobs/$JOB_DIR/**/*"
        - .gitlab-ci.yml
        - "common/**/*"

.job-on-release:
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: always
      allow_failure: false
      changes:
        - "jobs/$JOB_DIR/**/*"
        - .gitlab-ci.yml
        - "common/**/*"

.job-manual-on-release:
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: manual
      allow_failure: true
      changes:
        - "jobs/$JOB_DIR/**/*"
        - .gitlab-ci.yml
        - "common/**/*"

"$[[ inputs.job-dir ]]:lint":
  stage: lint
  image: crindiebimain.azurecr.io/dpt/ci-cd/python-poetry-$[[ inputs.python-version ]]-$[[ inputs.base-distro ]]:$[[ inputs.poetry-image-tag ]]
  variables:
    ENV: test
    JOB_DIR: $[[ inputs.job-dir ]]
    POETRY_CACHE_DIR: $CI_PROJECT_DIR/jobs/$JOB_DIR/.poetry-cache
    POETRY_VIRTUALENVS_IN_PROJECT: "true"

  script:
    - cd ./jobs/$JOB_DIR
    - poetry --version
    - poetry install
    - poetry run poe ci-lint
  rules:
    - !reference [.job-on-mr, rules]
  cache:
    key: global
    paths:
      - jobs/$JOB_DIR/.poetry-cache

"$[[ inputs.job-dir ]]:test":
  stage: test
  image: crindiebimain.azurecr.io/dpt/ci-cd/python-poetry-$[[ inputs.python-version ]]-$[[ inputs.base-distro ]]:$[[ inputs.poetry-image-tag ]]
  variables:
    ENV: test
    JOB_DIR: $[[ inputs.job-dir ]]
    POETRY_CACHE_DIR: $CI_PROJECT_DIR/jobs/$JOB_DIR/.poetry-cache
    POETRY_VIRTUALENVS_IN_PROJECT: "true"
  needs: []
  script:
    - cd ./jobs/$JOB_DIR
    - poetry --version
    - poetry install
    - poetry run poe ci-test $[[ inputs.pytest-extra-args ]]
    - $[[ inputs.extra-test-command ]]
  artifacts:
    when: always
    reports:
      junit: jobs/$JOB_DIR/pytest.xml
  rules:
    - !reference [.job-on-mr, rules]
  cache:
    key: global
    paths:
      - jobs/$JOB_DIR/.poetry-cache

"$[[ inputs.job-dir ]]:build":
  stage: build
  needs:
    - job: "$[[ inputs.job-dir ]]:lint"
      optional: true
    - job: "$[[ inputs.job-dir ]]:test"
      optional: true
  image:
    name: crindiebimain.azurecr.io/dpt/image-builder:$[[ inputs.builder-image-tag ]]
  variables:
    JOB_DIR: $[[ inputs.job-dir ]]
    PROJECT_DIR: $CI_PROJECT_DIR/jobs/$JOB_DIR
    FORCE_PROJECT_DIR: $[[ inputs.force-project-dir ]]
    JOB_DOCKERFILE: $[[ inputs.job-dockerfile ]]
    ARG_PYTHON_VERSION: $[[ inputs.python-version ]]
    ARG_BASE_DISTRO: $[[ inputs.base-distro ]]
    ARG_DATA_JOB_NAME: $JOB_DIR
  script:
    - export IMAGE_NAME=data-jobs/$(echo $JOB_DIR | tr _ -)
    - export PROJECT_DIR="${FORCE_PROJECT_DIR:-$PROJECT_DIR}"
    - echo $PROJECT_DIR
    # Kaniko requires Dockerfile to be inside of the context; copy it before build
    - cp $JOB_DOCKERFILE $PROJECT_DIR/Dockerfile
    # Copy job .dockerignore if context is different than job folder
    - |
      if [ "$CI_PROJECT_DIR/jobs/$JOB_DIR/.dockerignore" != "$PROJECT_DIR/.dockerignore" ]; then
        cp "$CI_PROJECT_DIR/jobs/$JOB_DIR/.dockerignore" "$PROJECT_DIR/.dockerignore"
      fi
    - image-exists && echo "Image already exists, skipping build!" && exit 0
    - image-builder
  rules:
    - !reference [.job-on-release, rules]
    - !reference [.job-on-mr, rules]

.deploy:
  dependencies: []
  variables:
    GIT_STRATEGY: none
  image:
    name: crindiebimain.azurecr.io/dpt/image-builder:$[[ inputs.builder-image-tag ]]
  script:
    - export IMAGE_NAME=data-jobs/$(echo $JOB_DIR | tr _ -)
    - tag-image $ENV
  allow_failure: false

"$[[ inputs.job-dir ]]:deploy_dev":
  extends: .deploy
  stage: deploy_dev
  needs:
    - "$[[ inputs.job-dir ]]:build"
  variables:
    ENV: dev
    JOB_DIR: $[[ inputs.job-dir ]]
  rules:
    - !reference [.job-manual-on-mr, rules]
    - !reference [.job-on-release, rules]

"$[[ inputs.job-dir ]]:deploy_prod":
  extends: .deploy
  stage: deploy_prod
  needs:
    - "$[[ inputs.job-dir ]]:build"
  variables:
    ENV: prod
    JOB_DIR: $[[ inputs.job-dir ]]
  rules:
    - !reference [.job-manual-on-release, rules]
