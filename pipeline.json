{"pipelines": [{"pipeline_name": "reports", "pipeline_definition": {"INIT": {"success": ["core_silver"], "failure": []}, "core_silver": {"success": ["saas_gold", "ppt_gold", "events_service_gold"], "failure": []}, "saas_gold": {"success": ["find_shards"], "failure": []}, "find_shards": {"success": ["direct_data_access_gold", "pbi_refresh"], "failure": []}, "pbi_refresh": {"success": ["processing_notification"], "failure": []}}, "triggered_by_events": ["REPORTS_CREATED", "REPORTS_DELETED", "SKUS_UPDATED", "REPORTS_TRIGGERED_BACKOFFICE"]}, {"pipeline_name": "data_sharing", "pipeline_definition": {"INIT": {"success": ["update_shared"], "failure": []}, "update_shared": {"success": ["direct_data_access_gold"], "failure": []}}, "triggered_by_events": ["DATA_SHARED", "DATA_UNSHARED"]}], "events": [{"name": "REPORTS_TRIGGERED_BACKOFFICE", "params_definition": ["studio_id", "observation_type", "portal"]}, {"name": "REPORTS_CREATED", "params_definition": ["studio_id", "observation_type", "portal", "report_ids"]}, {"name": "REPORTS_DELETED", "params_definition": ["studio_id", "observation_type", "portal", "report_ids"]}, {"name": "SKUS_UPDATED", "params_definition": ["studio_id", "observation_type", "portal", "sku_ids"]}, {"name": "STUDIO_DELETED", "params_definition": ["studio_id"]}, {"name": "DATA_SHARED", "params_definition": ["studio_id"]}, {"name": "DATA_UNSHARED", "params_definition": ["studio_id"]}]}